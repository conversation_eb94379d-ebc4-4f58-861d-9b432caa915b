<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار تسجيل الدخول الآن</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <style>body { font-family: Arial, sans-serif; }</style>
</head>
<body class='bg-light'>
<div class='container mt-5'>
<div class='card'>
<div class='card-header bg-danger text-white'>
<h3>🔧 اختبار تسجيل الدخول الآن</h3>
</div>
<div class='card-body'>";

// إنشاء مستخدم تجريبي فوري
if (isset($_GET['create'])) {
    $testUser = 'testuser' . time();
    $testPass = 'test123';
    
    try {
        $hashedPassword = password_hash($testPass, PASSWORD_DEFAULT);
        $stmt = $db->query("INSERT INTO users (username, password, role, is_active) VALUES (?, ?, 'teacher', 1)", 
                          [$testUser, $hashedPassword]);
        $userId = $db->lastInsertId();
        
        $stmt = $db->query("INSERT INTO teachers (user_id, employee_id, first_name, last_name, email, subject, grade_level, hire_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", 
                          [$userId, 'TEST' . time(), 'معلم', 'تجريبي', '<EMAIL>', 'اختبار', 'الصف الأول', date('Y-m-d')]);
        
        echo "<div class='alert alert-success'>
        <h4>✅ تم إنشاء مستخدم تجريبي!</h4>
        <p><strong>اسم المستخدم:</strong> <code style='background: yellow; padding: 5px;'>$testUser</code></p>
        <p><strong>كلمة المرور:</strong> <code style='background: yellow; padding: 5px;'>$testPass</code></p>
        <p><strong>استخدم هذه البيانات في النموذج أدناه ↓</strong></p>
        </div>";
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>خطأ: " . $e->getMessage() . "</div>";
    }
}

// اختبار تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    echo "<div class='alert alert-info'>
    <h5>📊 تشخيص مباشر:</h5>
    <p><strong>اسم المستخدم:</strong> '$username' (طول: " . strlen($username) . ")</p>
    <p><strong>كلمة المرور:</strong> '$password' (طول: " . strlen($password) . ")</p>
    </div>";
    
    if (empty($username) || empty($password)) {
        echo "<div class='alert alert-danger'>❌ البيانات فارغة!</div>";
    } else {
        try {
            $stmt = $db->query("SELECT * FROM users WHERE username = ? AND is_active = 1", [$username]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user) {
                echo "<div class='alert alert-success'>
                ✅ تم العثور على المستخدم!<br>
                ID: {$user['id']}<br>
                اسم المستخدم: '{$user['username']}'<br>
                نشط: " . ($user['is_active'] ? 'نعم' : 'لا') . "
                </div>";
                
                if (password_verify($password, $user['password'])) {
                    echo "<div class='alert alert-success'>
                    <h4>🎉 تسجيل الدخول نجح!</h4>
                    <p>كلمة المرور صحيحة!</p>
                    <a href='login.php' class='btn btn-success'>جرب الآن في صفحة تسجيل الدخول الحقيقية</a>
                    </div>";
                } else {
                    echo "<div class='alert alert-danger'>❌ كلمة المرور خاطئة!</div>";
                    
                    // اختبار كلمات مرور مختلفة
                    $testPasswords = ['test123', 'teacher123', 'debug123'];
                    foreach ($testPasswords as $testPass) {
                        if (password_verify($testPass, $user['password'])) {
                            echo "<div class='alert alert-warning'>✅ كلمة المرور الصحيحة: '$testPass'</div>";
                            break;
                        }
                    }
                }
            } else {
                echo "<div class='alert alert-danger'>❌ لم يتم العثور على المستخدم!</div>";
                
                // البحث بدون شرط النشاط
                $stmt = $db->query("SELECT * FROM users WHERE username = ?", [$username]);
                $userAny = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($userAny) {
                    echo "<div class='alert alert-warning'>
                    ⚠️ المستخدم موجود لكنه غير نشط!<br>
                    is_active = {$userAny['is_active']}<br>
                    سيتم تفعيله الآن...
                    </div>";
                    
                    $stmt = $db->query("UPDATE users SET is_active = 1 WHERE username = ?", [$username]);
                    echo "<div class='alert alert-success'>✅ تم تفعيل المستخدم! جرب مرة أخرى.</div>";
                } else {
                    echo "<div class='alert alert-danger'>❌ المستخدم غير موجود نهائياً!</div>";
                }
            }
            
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>❌ خطأ: " . $e->getMessage() . "</div>";
        }
    }
}

echo "<div class='row'>
<div class='col-md-6'>
<div class='card border-success'>
<div class='card-header bg-success text-white'>
<h5>1️⃣ إنشاء مستخدم تجريبي</h5>
</div>
<div class='card-body text-center'>
<p>أنشئ مستخدم تجريبي جديد للاختبار</p>
<a href='?create=1' class='btn btn-success btn-lg'>إنشاء مستخدم تجريبي</a>
</div>
</div>
</div>

<div class='col-md-6'>
<div class='card border-primary'>
<div class='card-header bg-primary text-white'>
<h5>2️⃣ اختبار تسجيل الدخول</h5>
</div>
<div class='card-body'>
<form method='POST'>
<div class='mb-3'>
<label class='form-label'>اسم المستخدم:</label>
<input type='text' class='form-control' name='username' required>
</div>
<div class='mb-3'>
<label class='form-label'>كلمة المرور:</label>
<input type='text' class='form-control' name='password' required>
</div>
<button type='submit' class='btn btn-primary w-100'>اختبار تسجيل الدخول</button>
</form>
</div>
</div>
</div>
</div>";

// عرض المستخدمين الحاليين
echo "<div class='mt-4'>
<h5>👥 المستخدمون الحاليون:</h5>";

try {
    $stmt = $db->query("SELECT username, is_active, created_at FROM users WHERE role = 'teacher' ORDER BY created_at DESC LIMIT 5");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($users)) {
        echo "<div class='table-responsive'>
        <table class='table table-striped'>
        <thead class='table-dark'>
        <tr><th>اسم المستخدم</th><th>نشط</th><th>تاريخ الإنشاء</th></tr>
        </thead>
        <tbody>";
        
        foreach ($users as $user) {
            $status = $user['is_active'] ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-danger">غير نشط</span>';
            $created = date('Y-m-d H:i', strtotime($user['created_at']));
            echo "<tr>
            <td><code>{$user['username']}</code></td>
            <td>$status</td>
            <td>$created</td>
            </tr>";
        }
        
        echo "</tbody></table></div>";
    } else {
        echo "<div class='alert alert-warning'>لا يوجد معلمون في النظام</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>خطأ في جلب المستخدمين: " . $e->getMessage() . "</div>";
}

echo "</div>";

echo "<div class='alert alert-primary mt-4'>
<h5>📋 تعليمات:</h5>
<ol>
<li><strong>اضغط على 'إنشاء مستخدم تجريبي'</strong> في الجانب الأيسر</li>
<li><strong>انسخ اسم المستخدم وكلمة المرور</strong> التي ستظهر</li>
<li><strong>ضعهما في النموذج</strong> في الجانب الأيمن</li>
<li><strong>اضغط 'اختبار تسجيل الدخول'</strong></li>
<li><strong>تابع الرسائل</strong> لمعرفة النتيجة</li>
</ol>
</div>";

echo "</div>
</div>
</div>
</body>
</html>";
?>
