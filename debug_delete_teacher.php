<?php
require_once 'config/config.php';

// التحقق من تسجيل الدخول وصلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    redirect('login.php');
}

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تشخيص حذف المعلم - نظام أرشفة الأساتذة</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap' rel='stylesheet'>
    <style>body { font-family: 'Cairo', sans-serif; }</style>
</head>
<body class='bg-light'>
<div class='container mt-5'>
<div class='row justify-content-center'>
<div class='col-md-10'>
<div class='card'>
<div class='card-header bg-warning text-dark'>
<h4 class='mb-0'><i class='fas fa-bug me-2'></i>تشخيص حذف المعلم</h4>
</div>
<div class='card-body'>";

// معالجة اختبار الحذف
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_delete'])) {
    $teacherId = (int)($_POST['teacher_id'] ?? 0);
    
    if ($teacherId > 0) {
        echo "<h5 class='text-primary'><i class='fas fa-test-tube me-2'></i>اختبار حذف المعلم ID: $teacherId</h5>";
        
        try {
            // جلب بيانات المعلم
            $stmt = $db->query("SELECT t.*, u.id as user_id, u.username 
                               FROM teachers t 
                               LEFT JOIN users u ON t.user_id = u.id 
                               WHERE t.id = ?", [$teacherId]);
            $teacher = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$teacher) {
                echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>المعلم غير موجود</div>";
            } else {
                echo "<div class='alert alert-info'>
                <strong>بيانات المعلم:</strong><br>
                الاسم: {$teacher['first_name']} {$teacher['last_name']}<br>
                رقم الموظف: {$teacher['employee_id']}<br>
                حساب المستخدم: " . ($teacher['user_id'] ? "موجود (ID: {$teacher['user_id']})" : "غير موجود") . "
                </div>";
                
                // فحص البيانات المرتبطة
                $relatedData = [];
                $tables = [
                    'daily_preparations' => 'التحضيرات اليومية',
                    'activities' => 'الأنشطة والمسابقات',
                    'curriculum_progress' => 'وحدات المنهج',
                    'files' => 'الملفات',
                    'warnings' => 'التنويهات',
                    'qualifications' => 'المؤهلات',
                    'experiences' => 'الخبرات'
                ];
                
                echo "<h6 class='text-secondary mt-3'>البيانات المرتبطة:</h6>";
                foreach ($tables as $table => $description) {
                    try {
                        $stmt = $db->query("SELECT COUNT(*) FROM $table WHERE teacher_id = ?", [$teacherId]);
                        $count = $stmt->fetchColumn();
                        
                        if ($count > 0) {
                            $relatedData[] = "$count $description";
                            echo "<div class='alert alert-warning alert-sm'>$description: $count سجل</div>";
                        } else {
                            echo "<div class='alert alert-success alert-sm'>$description: لا توجد بيانات</div>";
                        }
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger alert-sm'>$description: خطأ - " . $e->getMessage() . "</div>";
                    }
                }
                
                // محاولة الحذف الفعلي
                if (isset($_POST['confirm_delete'])) {
                    echo "<h6 class='text-danger mt-3'>تنفيذ الحذف الفعلي:</h6>";
                    
                    try {
                        $db->getConnection()->beginTransaction();
                        
                        // حذف البيانات المرتبطة
                        foreach (array_keys($tables) as $table) {
                            try {
                                $stmt = $db->query("DELETE FROM $table WHERE teacher_id = ?", [$teacherId]);
                                $deletedRows = $stmt->rowCount();
                                echo "<div class='alert alert-info alert-sm'>تم حذف $deletedRows سجل من جدول $table</div>";
                            } catch (Exception $e) {
                                echo "<div class='alert alert-warning alert-sm'>تعذر حذف من جدول $table: " . $e->getMessage() . "</div>";
                            }
                        }
                        
                        // حذف المعلم
                        $stmt = $db->query("DELETE FROM teachers WHERE id = ?", [$teacherId]);
                        $deletedTeacher = $stmt->rowCount();
                        echo "<div class='alert alert-info alert-sm'>تم حذف $deletedTeacher معلم من جدول teachers</div>";
                        
                        // حذف حساب المستخدم
                        if ($teacher['user_id']) {
                            $stmt = $db->query("DELETE FROM users WHERE id = ?", [$teacher['user_id']]);
                            $deletedUser = $stmt->rowCount();
                            echo "<div class='alert alert-info alert-sm'>تم حذف $deletedUser مستخدم من جدول users</div>";
                        }
                        
                        $db->getConnection()->commit();
                        
                        echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>تم حذف المعلم بنجاح!</div>";
                        
                    } catch (Exception $e) {
                        $db->getConnection()->rollBack();
                        echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ في الحذف: " . $e->getMessage() . "</div>";
                    }
                } else {
                    echo "<form method='POST' class='mt-3'>
                    <input type='hidden' name='teacher_id' value='$teacherId'>
                    <input type='hidden' name='test_delete' value='1'>
                    <input type='hidden' name='confirm_delete' value='1'>
                    <button type='submit' class='btn btn-danger' onclick='return confirm(\"هل أنت متأكد من حذف هذا المعلم نهائياً؟\")'>
                        <i class='fas fa-trash me-2'></i>تأكيد الحذف النهائي
                    </button>
                    </form>";
                }
            }
            
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ في اختبار الحذف: " . $e->getMessage() . "</div>";
        }
    }
}

// عرض قائمة المعلمين للاختبار
echo "<h5 class='text-primary'><i class='fas fa-users me-2'></i>اختر معلماً لاختبار حذفه:</h5>";

try {
    $stmt = $db->query("SELECT t.*, u.username 
                       FROM teachers t 
                       LEFT JOIN users u ON t.user_id = u.id 
                       ORDER BY t.first_name, t.last_name");
    $teachers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($teachers)) {
        echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle me-2'></i>لا يوجد معلمون في النظام</div>";
    } else {
        echo "<div class='table-responsive'>
        <table class='table table-striped table-sm'>
        <thead class='table-dark'>
        <tr>
        <th>ID</th>
        <th>الاسم</th>
        <th>رقم الموظف</th>
        <th>المادة</th>
        <th>حساب المستخدم</th>
        <th>اختبار الحذف</th>
        </tr>
        </thead>
        <tbody>";
        
        foreach ($teachers as $teacher) {
            $teacherName = $teacher['first_name'] . ' ' . $teacher['last_name'];
            $userAccount = $teacher['username'] ? $teacher['username'] : 'لا يوجد';
            
            echo "<tr>
            <td>{$teacher['id']}</td>
            <td><strong>" . htmlspecialchars($teacherName) . "</strong></td>
            <td>" . htmlspecialchars($teacher['employee_id']) . "</td>
            <td>" . htmlspecialchars($teacher['subject']) . "</td>
            <td>$userAccount</td>
            <td>
                <form method='POST' style='display: inline;'>
                    <input type='hidden' name='teacher_id' value='{$teacher['id']}'>
                    <input type='hidden' name='test_delete' value='1'>
                    <button type='submit' class='btn btn-sm btn-outline-warning'>
                        <i class='fas fa-test-tube me-1'></i>اختبار
                    </button>
                </form>
            </td>
            </tr>";
        }
        
        echo "</tbody></table></div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ في جلب قائمة المعلمين: " . $e->getMessage() . "</div>";
}

// فحص بنية قاعدة البيانات
echo "<h5 class='text-primary mt-4'><i class='fas fa-database me-2'></i>فحص بنية قاعدة البيانات:</h5>";

$tables = ['teachers', 'users', 'daily_preparations', 'activities', 'curriculum_progress', 'files', 'warnings', 'qualifications', 'experiences'];

foreach ($tables as $table) {
    try {
        $stmt = $db->query("SELECT COUNT(*) FROM $table");
        $count = $stmt->fetchColumn();
        echo "<div class='alert alert-success alert-sm'>جدول $table: $count سجل</div>";
    } catch (Exception $e) {
        echo "<div class='alert alert-danger alert-sm'>جدول $table: خطأ - " . $e->getMessage() . "</div>";
    }
}

// روابط مفيدة
echo "<div class='alert alert-primary mt-4'>
<h5><i class='fas fa-link me-2'></i>روابط مفيدة:</h5>
<div class='row'>
<div class='col-md-6'>
<ul class='list-unstyled'>
<li><a href='teachers/list.php' class='btn btn-sm btn-outline-primary me-2 mb-2'><i class='fas fa-users me-1'></i>قائمة المعلمين</a></li>
<li><a href='teachers/add.php' class='btn btn-sm btn-outline-success me-2 mb-2'><i class='fas fa-user-plus me-1'></i>إضافة معلم جديد</a></li>
</ul>
</div>
<div class='col-md-6'>
<ul class='list-unstyled'>
<li><a href='fix_database_columns.php' class='btn btn-sm btn-outline-info me-2 mb-2'><i class='fas fa-database me-1'></i>فحص قاعدة البيانات</a></li>
<li><a href='index.php' class='btn btn-sm btn-outline-secondary me-2 mb-2'><i class='fas fa-home me-1'></i>الصفحة الرئيسية</a></li>
</ul>
</div>
</div>
</div>";

echo "</div>
</div>
</div>
</div>
</div>
</body>
</html>";
?>
