<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تشخيص إنشاء المعلم وتسجيل الدخول</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap' rel='stylesheet'>
    <style>body { font-family: 'Cairo', sans-serif; }</style>
</head>
<body class='bg-light'>
<div class='container mt-5'>
<div class='row justify-content-center'>
<div class='col-md-10'>
<div class='card'>
<div class='card-header bg-warning text-dark'>
<h4 class='mb-0'><i class='fas fa-bug me-2'></i>تشخيص إنشاء المعلم وتسجيل الدخول</h4>
</div>
<div class='card-body'>";

// اختبار إنشاء معلم جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_test_teacher'])) {
    echo "<h5 class='text-primary'><i class='fas fa-user-plus me-2'></i>اختبار إنشاء معلم جديد:</h5>";
    
    $testUsername = 'test_' . time();
    $testPassword = 'test123456';
    $testEmployeeId = 'EMP' . time();
    
    try {
        $db->getConnection()->beginTransaction();
        
        echo "<div class='alert alert-info'>
        <strong>البيانات التجريبية:</strong><br>
        اسم المستخدم: $testUsername<br>
        كلمة المرور: $testPassword<br>
        رقم الموظف: $testEmployeeId
        </div>";
        
        // إنشاء المستخدم
        $hashedPassword = password_hash($testPassword, PASSWORD_DEFAULT);
        echo "<div class='alert alert-secondary'>كلمة المرور المشفرة: " . substr($hashedPassword, 0, 50) . "...</div>";
        
        $stmt = $db->query("INSERT INTO users (username, password, role, is_active) VALUES (?, ?, 'teacher', 1)", 
                          [$testUsername, $hashedPassword]);
        $userId = $db->lastInsertId();
        
        echo "<div class='alert alert-success'>تم إنشاء المستخدم بنجاح - ID: $userId</div>";
        
        // إنشاء المعلم
        $stmt = $db->query("INSERT INTO teachers (user_id, employee_id, first_name, last_name, email, subject, grade_level, hire_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", 
                          [$userId, $testEmployeeId, 'معلم', 'تجريبي', '<EMAIL>', 'اختبار', 'الصف الأول', date('Y-m-d')]);
        $teacherId = $db->lastInsertId();
        
        echo "<div class='alert alert-success'>تم إنشاء المعلم بنجاح - ID: $teacherId</div>";
        
        $db->getConnection()->commit();
        
        // اختبار تسجيل الدخول فوراً
        echo "<h6 class='text-danger mt-3'>اختبار تسجيل الدخول:</h6>";
        
        $stmt = $db->query("SELECT * FROM users WHERE username = ?", [$testUsername]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            echo "<div class='alert alert-info'>تم العثور على المستخدم في قاعدة البيانات</div>";
            
            if (password_verify($testPassword, $user['password'])) {
                echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>كلمة المرور صحيحة - يجب أن يعمل تسجيل الدخول!</div>";
                
                if ($user['is_active']) {
                    echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>الحساب نشط</div>";
                } else {
                    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>الحساب غير نشط!</div>";
                }
            } else {
                echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>كلمة المرور خاطئة!</div>";
            }
        } else {
            echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>لم يتم العثور على المستخدم!</div>";
        }
        
        // نموذج اختبار تسجيل الدخول
        echo "<div class='card mt-3'>
        <div class='card-header'>
        <h6 class='mb-0'>اختبار تسجيل الدخول بالبيانات الجديدة</h6>
        </div>
        <div class='card-body'>
        <form method='POST'>
        <input type='hidden' name='test_login' value='1'>
        <input type='hidden' name='test_username' value='$testUsername'>
        <input type='hidden' name='test_password' value='$testPassword'>
        <button type='submit' class='btn btn-primary'>اختبار تسجيل الدخول</button>
        <a href='login.php' class='btn btn-success ms-2'>الذهاب لصفحة تسجيل الدخول</a>
        </form>
        </div>
        </div>";
        
    } catch (Exception $e) {
        $db->getConnection()->rollBack();
        echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ في إنشاء المعلم: " . $e->getMessage() . "</div>";
    }
}

// اختبار تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_login'])) {
    $testUsername = $_POST['test_username'];
    $testPassword = $_POST['test_password'];
    
    echo "<h5 class='text-primary'><i class='fas fa-sign-in-alt me-2'></i>اختبار تسجيل الدخول:</h5>";
    
    try {
        $stmt = $db->query("SELECT * FROM users WHERE username = ?", [$testUsername]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            echo "<div class='alert alert-info'>
            <strong>بيانات المستخدم:</strong><br>
            ID: {$user['id']}<br>
            اسم المستخدم: {$user['username']}<br>
            الدور: {$user['role']}<br>
            نشط: " . ($user['is_active'] ? 'نعم' : 'لا') . "<br>
            تاريخ الإنشاء: {$user['created_at']}
            </div>";
            
            if (password_verify($testPassword, $user['password'])) {
                echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>تسجيل الدخول نجح!</div>";
                
                // محاولة تسجيل الدخول الفعلي
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['role'] = $user['role'];
                
                echo "<div class='alert alert-success'>
                <i class='fas fa-check me-2'></i>تم تسجيل الدخول بنجاح في الجلسة!<br>
                <a href='index.php' class='btn btn-success mt-2'>الذهاب للصفحة الرئيسية</a>
                </div>";
                
            } else {
                echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>كلمة المرور خاطئة!</div>";
            }
        } else {
            echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>المستخدم غير موجود!</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ في اختبار تسجيل الدخول: " . $e->getMessage() . "</div>";
    }
}

// عرض المعلمين الحاليين
echo "<h5 class='text-primary'><i class='fas fa-users me-2'></i>المعلمون الحاليون:</h5>";

try {
    $stmt = $db->query("SELECT t.*, u.username, u.is_active, u.created_at as user_created 
                       FROM teachers t 
                       LEFT JOIN users u ON t.user_id = u.id 
                       ORDER BY t.id DESC 
                       LIMIT 10");
    $teachers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($teachers)) {
        echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle me-2'></i>لا يوجد معلمون في النظام</div>";
    } else {
        echo "<div class='table-responsive'>
        <table class='table table-striped table-sm'>
        <thead class='table-dark'>
        <tr>
        <th>ID</th>
        <th>الاسم</th>
        <th>رقم الموظف</th>
        <th>اسم المستخدم</th>
        <th>نشط</th>
        <th>تاريخ الإنشاء</th>
        <th>اختبار الدخول</th>
        </tr>
        </thead>
        <tbody>";
        
        foreach ($teachers as $teacher) {
            $teacherName = $teacher['first_name'] . ' ' . $teacher['last_name'];
            $username = $teacher['username'] ?: 'لا يوجد';
            $isActive = $teacher['is_active'] ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-danger">غير نشط</span>';
            $userCreated = $teacher['user_created'] ? date('Y-m-d H:i', strtotime($teacher['user_created'])) : '-';
            
            echo "<tr>
            <td>{$teacher['id']}</td>
            <td><strong>" . htmlspecialchars($teacherName) . "</strong></td>
            <td>" . htmlspecialchars($teacher['employee_id']) . "</td>
            <td>$username</td>
            <td>$isActive</td>
            <td>$userCreated</td>
            <td>";
            
            if ($teacher['username']) {
                echo "<button class='btn btn-sm btn-outline-primary' onclick='testUserLogin(\"{$teacher['username']}\")'>اختبار</button>";
            } else {
                echo "<span class='text-muted'>لا يوجد حساب</span>";
            }
            
            echo "</td>
            </tr>";
        }
        
        echo "</tbody></table></div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ في جلب المعلمين: " . $e->getMessage() . "</div>";
}

// نموذج إنشاء معلم تجريبي
echo "<div class='mt-4'>
<div class='card'>
<div class='card-header'>
<h5 class='mb-0'><i class='fas fa-plus me-2'></i>إنشاء معلم تجريبي للاختبار</h5>
</div>
<div class='card-body'>
<form method='POST'>
<button type='submit' name='create_test_teacher' class='btn btn-primary'>
<i class='fas fa-user-plus me-2'></i>إنشاء معلم تجريبي
</button>
</form>
<small class='text-muted'>سيتم إنشاء معلم بأسم مستخدم وكلمة مرور تجريبية واختبار تسجيل الدخول فوراً</small>
</div>
</div>
</div>";

// روابط مفيدة
echo "<div class='alert alert-primary mt-4'>
<h5><i class='fas fa-link me-2'></i>روابط مفيدة:</h5>
<div class='row'>
<div class='col-md-6'>
<ul class='list-unstyled'>
<li><a href='teachers/add.php' class='btn btn-sm btn-outline-success me-2 mb-2'><i class='fas fa-user-plus me-1'></i>إضافة معلم جديد</a></li>
<li><a href='login.php' class='btn btn-sm btn-outline-primary me-2 mb-2'><i class='fas fa-sign-in-alt me-1'></i>صفحة تسجيل الدخول</a></li>
</ul>
</div>
<div class='col-md-6'>
<ul class='list-unstyled'>
<li><a href='teachers/list.php' class='btn btn-sm btn-outline-info me-2 mb-2'><i class='fas fa-users me-1'></i>قائمة المعلمين</a></li>
<li><a href='index.php' class='btn btn-sm btn-outline-secondary me-2 mb-2'><i class='fas fa-home me-1'></i>الصفحة الرئيسية</a></li>
</ul>
</div>
</div>
</div>";

echo "</div>
</div>
</div>
</div>
</div>

<script>
function testUserLogin(username) {
    const password = prompt('أدخل كلمة المرور لاختبار تسجيل الدخول للمستخدم: ' + username);
    if (password) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type='hidden' name='test_login' value='1'>
            <input type='hidden' name='test_username' value='\${username}'>
            <input type='hidden' name='test_password' value='\${password}'>
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

</body>
</html>";
?>
