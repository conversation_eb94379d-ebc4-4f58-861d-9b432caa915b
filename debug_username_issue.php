<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تشخيص مشكلة اسم المستخدم</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap' rel='stylesheet'>
    <style>body { font-family: 'Cairo', sans-serif; }</style>
</head>
<body class='bg-light'>
<div class='container mt-5'>
<div class='row justify-content-center'>
<div class='col-md-10'>
<div class='card'>
<div class='card-header bg-danger text-white'>
<h4 class='mb-0'><i class='fas fa-exclamation-triangle me-2'></i>تشخيص مشكلة اسم المستخدم</h4>
</div>
<div class='card-body'>";

// اختبار إنشاء معلم جديد مع تتبع مفصل
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_create'])) {
    $testUsername = trim($_POST['test_username']);
    $testPassword = trim($_POST['test_password']);
    $testEmployeeId = 'TEST' . time();
    
    echo "<h5 class='text-primary'><i class='fas fa-user-plus me-2'></i>اختبار إنشاء معلم جديد:</h5>";
    
    echo "<div class='alert alert-info'>
    <strong>البيانات المدخلة:</strong><br>
    اسم المستخدم: '$testUsername' (طول: " . strlen($testUsername) . " حرف)<br>
    كلمة المرور: '$testPassword' (طول: " . strlen($testPassword) . " حرف)<br>
    رقم الموظف: $testEmployeeId
    </div>";
    
    // فحص ما إذا كان اسم المستخدم موجود مسبقاً
    try {
        $stmt = $db->query("SELECT COUNT(*) FROM users WHERE username = ?", [$testUsername]);
        $existingCount = $stmt->fetchColumn();
        
        if ($existingCount > 0) {
            echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle me-2'></i>اسم المستخدم '$testUsername' موجود بالفعل!</div>";
        } else {
            echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>اسم المستخدم '$testUsername' غير موجود - يمكن استخدامه</div>";
            
            try {
                $db->getConnection()->beginTransaction();
                
                // إنشاء المستخدم
                $hashedPassword = password_hash($testPassword, PASSWORD_DEFAULT);
                echo "<div class='alert alert-secondary'>كلمة المرور المشفرة: " . substr($hashedPassword, 0, 50) . "...</div>";
                
                $stmt = $db->query("INSERT INTO users (username, password, role, is_active) VALUES (?, ?, 'teacher', 1)", 
                                  [$testUsername, $hashedPassword]);
                $userId = $db->lastInsertId();
                
                echo "<div class='alert alert-success'>تم إنشاء المستخدم بنجاح - ID: $userId</div>";
                
                // إنشاء المعلم
                $stmt = $db->query("INSERT INTO teachers (user_id, employee_id, first_name, last_name, email, subject, grade_level, hire_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", 
                                  [$userId, $testEmployeeId, 'معلم', 'اختبار', '<EMAIL>', 'اختبار', 'الصف الأول', date('Y-m-d')]);
                $teacherId = $db->lastInsertId();
                
                echo "<div class='alert alert-success'>تم إنشاء المعلم بنجاح - ID: $teacherId</div>";
                
                $db->getConnection()->commit();
                
                // فحص فوري للمستخدم المنشأ
                echo "<h6 class='text-info mt-3'>فحص فوري للمستخدم المنشأ:</h6>";
                
                $stmt = $db->query("SELECT * FROM users WHERE id = ?", [$userId]);
                $createdUser = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($createdUser) {
                    echo "<div class='alert alert-info'>
                    <strong>بيانات المستخدم في قاعدة البيانات:</strong><br>
                    ID: {$createdUser['id']}<br>
                    اسم المستخدم: '{$createdUser['username']}' (طول: " . strlen($createdUser['username']) . " حرف)<br>
                    الدور: {$createdUser['role']}<br>
                    نشط: " . ($createdUser['is_active'] ? 'نعم' : 'لا') . "<br>
                    تاريخ الإنشاء: {$createdUser['created_at']}
                    </div>";
                    
                    // اختبار تسجيل الدخول فوراً
                    echo "<h6 class='text-danger mt-3'>اختبار تسجيل الدخول فوراً:</h6>";
                    
                    // البحث بنفس اسم المستخدم المدخل
                    $stmt = $db->query("SELECT * FROM users WHERE username = ?", [$testUsername]);
                    $foundUser = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($foundUser) {
                        echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>تم العثور على المستخدم بنجاح!</div>";
                        
                        if (password_verify($testPassword, $foundUser['password'])) {
                            echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>كلمة المرور صحيحة!</div>";
                            
                            if ($foundUser['is_active']) {
                                echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>الحساب نشط - يجب أن يعمل تسجيل الدخول!</div>";
                                
                                // نموذج اختبار تسجيل الدخول
                                echo "<div class='card mt-3'>
                                <div class='card-header bg-success text-white'>
                                <h6 class='mb-0'>اختبار تسجيل الدخول</h6>
                                </div>
                                <div class='card-body'>
                                <form method='POST'>
                                <input type='hidden' name='login_test' value='1'>
                                <input type='hidden' name='login_username' value='" . htmlspecialchars($testUsername) . "'>
                                <input type='hidden' name='login_password' value='" . htmlspecialchars($testPassword) . "'>
                                <p><strong>اسم المستخدم:</strong> $testUsername</p>
                                <p><strong>كلمة المرور:</strong> $testPassword</p>
                                <button type='submit' class='btn btn-success'>اختبار تسجيل الدخول</button>
                                <a href='login.php' class='btn btn-primary ms-2'>الذهاب لصفحة تسجيل الدخول</a>
                                </form>
                                </div>
                                </div>";
                                
                            } else {
                                echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>الحساب غير نشط!</div>";
                            }
                        } else {
                            echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>كلمة المرور خاطئة!</div>";
                        }
                    } else {
                        echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>لم يتم العثور على المستخدم بنفس اسم المستخدم!</div>";
                        
                        // فحص جميع المستخدمين للمقارنة
                        echo "<h6 class='text-warning mt-3'>فحص جميع أسماء المستخدمين:</h6>";
                        $stmt = $db->query("SELECT username FROM users ORDER BY id DESC LIMIT 10");
                        $allUsers = $stmt->fetchAll(PDO::FETCH_COLUMN);
                        
                        echo "<div class='alert alert-warning'>";
                        echo "<strong>آخر 10 أسماء مستخدمين:</strong><br>";
                        foreach ($allUsers as $username) {
                            echo "- '$username' (طول: " . strlen($username) . " حرف)<br>";
                        }
                        echo "</div>";
                    }
                } else {
                    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>لم يتم العثور على المستخدم المنشأ!</div>";
                }
                
            } catch (Exception $e) {
                $db->getConnection()->rollBack();
                echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ في إنشاء المعلم: " . $e->getMessage() . "</div>";
            }
        }
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ في فحص اسم المستخدم: " . $e->getMessage() . "</div>";
    }
}

// اختبار تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login_test'])) {
    $loginUsername = trim($_POST['login_username']);
    $loginPassword = trim($_POST['login_password']);
    
    echo "<h5 class='text-primary'><i class='fas fa-sign-in-alt me-2'></i>اختبار تسجيل الدخول:</h5>";
    
    echo "<div class='alert alert-info'>
    <strong>بيانات تسجيل الدخول:</strong><br>
    اسم المستخدم: '$loginUsername' (طول: " . strlen($loginUsername) . " حرف)<br>
    كلمة المرور: '$loginPassword' (طول: " . strlen($loginPassword) . " حرف)
    </div>";
    
    try {
        // البحث الدقيق
        $stmt = $db->query("SELECT * FROM users WHERE username = ? AND is_active = 1", [$loginUsername]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>تم العثور على المستخدم!</div>";
            
            echo "<div class='alert alert-info'>
            <strong>بيانات المستخدم:</strong><br>
            ID: {$user['id']}<br>
            اسم المستخدم: '{$user['username']}'<br>
            الدور: {$user['role']}<br>
            نشط: " . ($user['is_active'] ? 'نعم' : 'لا') . "
            </div>";
            
            if (password_verify($loginPassword, $user['password'])) {
                echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>تسجيل الدخول نجح!</div>";
                
                // تسجيل الدخول الفعلي
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['role'] = $user['role'];
                
                echo "<div class='alert alert-success'>
                <i class='fas fa-check me-2'></i>تم تسجيل الدخول في الجلسة!<br>
                <a href='index.php' class='btn btn-success mt-2'>الذهاب للصفحة الرئيسية</a>
                </div>";
                
            } else {
                echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>كلمة المرور خاطئة!</div>";
            }
        } else {
            echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>لم يتم العثور على المستخدم!</div>";
            
            // البحث بدون شرط is_active
            $stmt = $db->query("SELECT * FROM users WHERE username = ?", [$loginUsername]);
            $userInactive = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($userInactive) {
                echo "<div class='alert alert-warning'>المستخدم موجود لكنه غير نشط!</div>";
            } else {
                echo "<div class='alert alert-danger'>المستخدم غير موجود نهائياً!</div>";
                
                // البحث المشابه
                $stmt = $db->query("SELECT username FROM users WHERE username LIKE ?", ['%' . $loginUsername . '%']);
                $similarUsers = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                if (!empty($similarUsers)) {
                    echo "<div class='alert alert-info'>
                    <strong>أسماء مستخدمين مشابهة:</strong><br>";
                    foreach ($similarUsers as $similar) {
                        echo "- '$similar'<br>";
                    }
                    echo "</div>";
                }
            }
        }
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ في اختبار تسجيل الدخول: " . $e->getMessage() . "</div>";
    }
}

// نموذج اختبار إنشاء معلم
echo "<h5 class='text-primary'><i class='fas fa-test-tube me-2'></i>اختبار إنشاء معلم جديد:</h5>";

echo "<div class='card'>
<div class='card-header'>
<h6 class='mb-0'>إنشاء معلم تجريبي</h6>
</div>
<div class='card-body'>
<form method='POST'>
<div class='row'>
<div class='col-md-6 mb-3'>
<label for='test_username' class='form-label'>اسم المستخدم:</label>
<input type='text' class='form-control' id='test_username' name='test_username' 
       value='testuser" . time() . "' required>
</div>
<div class='col-md-6 mb-3'>
<label for='test_password' class='form-label'>كلمة المرور:</label>
<input type='text' class='form-control' id='test_password' name='test_password' 
       value='testpass123' required>
</div>
</div>
<button type='submit' name='test_create' class='btn btn-primary'>
<i class='fas fa-user-plus me-2'></i>إنشاء واختبار
</button>
</form>
</div>
</div>";

// عرض آخر المستخدمين المنشأين
echo "<h5 class='text-primary mt-4'><i class='fas fa-users me-2'></i>آخر المستخدمين المنشأين:</h5>";

try {
    $stmt = $db->query("SELECT u.*, t.first_name, t.last_name 
                       FROM users u 
                       LEFT JOIN teachers t ON u.id = t.user_id 
                       WHERE u.role = 'teacher' 
                       ORDER BY u.created_at DESC 
                       LIMIT 10");
    $recentUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($recentUsers)) {
        echo "<div class='alert alert-warning'>لا يوجد معلمون في النظام</div>";
    } else {
        echo "<div class='table-responsive'>
        <table class='table table-striped table-sm'>
        <thead class='table-dark'>
        <tr>
        <th>ID</th>
        <th>اسم المستخدم</th>
        <th>اسم المعلم</th>
        <th>نشط</th>
        <th>تاريخ الإنشاء</th>
        <th>اختبار</th>
        </tr>
        </thead>
        <tbody>";
        
        foreach ($recentUsers as $user) {
            $teacherName = $user['first_name'] ? $user['first_name'] . ' ' . $user['last_name'] : 'غير محدد';
            $isActive = $user['is_active'] ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-danger">غير نشط</span>';
            $createdAt = date('Y-m-d H:i', strtotime($user['created_at']));
            
            echo "<tr>
            <td>{$user['id']}</td>
            <td><code>{$user['username']}</code></td>
            <td>" . htmlspecialchars($teacherName) . "</td>
            <td>$isActive</td>
            <td>$createdAt</td>
            <td>
                <button class='btn btn-sm btn-outline-primary' onclick='testExistingUser(\"{$user['username']}\")'>
                    اختبار
                </button>
            </td>
            </tr>";
        }
        
        echo "</tbody></table></div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ في جلب المستخدمين: " . $e->getMessage() . "</div>";
}

// روابط مفيدة
echo "<div class='alert alert-primary mt-4'>
<h5><i class='fas fa-link me-2'></i>روابط مفيدة:</h5>
<div class='row'>
<div class='col-md-6'>
<ul class='list-unstyled'>
<li><a href='teachers/add.php' class='btn btn-sm btn-outline-success me-2 mb-2'><i class='fas fa-user-plus me-1'></i>إضافة معلم جديد</a></li>
<li><a href='login.php' class='btn btn-sm btn-outline-primary me-2 mb-2'><i class='fas fa-sign-in-alt me-1'></i>صفحة تسجيل الدخول</a></li>
</ul>
</div>
<div class='col-md-6'>
<ul class='list-unstyled'>
<li><a href='fix_teacher_login.php' class='btn btn-sm btn-outline-warning me-2 mb-2'><i class='fas fa-wrench me-1'></i>إصلاح تسجيل الدخول</a></li>
<li><a href='index.php' class='btn btn-sm btn-outline-secondary me-2 mb-2'><i class='fas fa-home me-1'></i>الصفحة الرئيسية</a></li>
</ul>
</div>
</div>
</div>";

echo "</div>
</div>
</div>
</div>
</div>

<script>
function testExistingUser(username) {
    const password = prompt('أدخل كلمة المرور لاختبار المستخدم: ' + username);
    if (password) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type='hidden' name='login_test' value='1'>
            <input type='hidden' name='login_username' value='\${username}'>
            <input type='hidden' name='login_password' value='\${password}'>
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

</body>
</html>";
?>
