<?php
require_once 'config/config.php';

// تفعيل عرض الأخطاء للتشخيص
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تشخيص مشكلة تسجيل الدخول</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap' rel='stylesheet'>
    <style>body { font-family: 'Cairo', sans-serif; }</style>
</head>
<body class='bg-light'>
<div class='container mt-5'>
<div class='row justify-content-center'>
<div class='col-md-10'>
<div class='card'>
<div class='card-header bg-danger text-white'>
<h4 class='mb-0'><i class='fas fa-bug me-2'></i>تشخيص مشكلة تسجيل الدخول</h4>
</div>
<div class='card-body'>";

// إنشاء معلم تجريبي أولاً
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_test_user'])) {
    $testUsername = 'debuguser' . time();
    $testPassword = 'debug123';
    
    echo "<h5 class='text-primary'><i class='fas fa-plus me-2'></i>إنشاء مستخدم تجريبي:</h5>";
    
    try {
        $db->getConnection()->beginTransaction();
        
        // إنشاء المستخدم
        $hashedPassword = password_hash($testPassword, PASSWORD_DEFAULT);
        $stmt = $db->query("INSERT INTO users (username, password, role, is_active, created_at) VALUES (?, ?, 'teacher', 1, datetime('now'))", 
                          [$testUsername, $hashedPassword]);
        $userId = $db->lastInsertId();
        
        // إنشاء المعلم
        $stmt = $db->query("INSERT INTO teachers (user_id, employee_id, first_name, last_name, email, subject, grade_level, hire_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", 
                          [$userId, 'DEBUG' . time(), 'معلم', 'تجريبي', '<EMAIL>', 'اختبار', 'الصف الأول', date('Y-m-d')]);
        
        $db->getConnection()->commit();
        
        echo "<div class='alert alert-success'>
        <h6>تم إنشاء المستخدم التجريبي بنجاح!</h6>
        <p><strong>اسم المستخدم:</strong> <code>$testUsername</code></p>
        <p><strong>كلمة المرور:</strong> <code>$testPassword</code></p>
        <p>استخدم هذه البيانات في النموذج أدناه لاختبار تسجيل الدخول.</p>
        </div>";
        
        // حفظ البيانات للاستخدام
        $_SESSION['debug_username'] = $testUsername;
        $_SESSION['debug_password'] = $testPassword;
        
    } catch (Exception $e) {
        $db->getConnection()->rollBack();
        echo "<div class='alert alert-danger'>خطأ في إنشاء المستخدم: " . $e->getMessage() . "</div>";
    }
}

// اختبار تسجيل الدخول مع تتبع مفصل
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_login'])) {
    $inputUsername = $_POST['test_username'] ?? '';
    $inputPassword = $_POST['test_password'] ?? '';
    
    echo "<h5 class='text-danger'><i class='fas fa-search me-2'></i>تشخيص تسجيل الدخول:</h5>";
    
    echo "<div class='alert alert-info'>
    <h6>البيانات المدخلة:</h6>
    <p><strong>اسم المستخدم:</strong> '<span style='background: yellow; padding: 2px;'>$inputUsername</span>' (طول: " . strlen($inputUsername) . ")</p>
    <p><strong>كلمة المرور:</strong> '<span style='background: yellow; padding: 2px;'>$inputPassword</span>' (طول: " . strlen($inputPassword) . ")</p>
    </div>";
    
    // خطوة 1: معالجة البيانات (نفس ما يحدث في login.php)
    $processedUsername = trim($inputUsername);
    $processedPassword = $inputPassword;
    
    echo "<div class='alert alert-secondary'>
    <h6>خطوة 1: معالجة البيانات</h6>
    <p><strong>اسم المستخدم بعد trim:</strong> '<span style='background: lightblue; padding: 2px;'>$processedUsername</span>' (طول: " . strlen($processedUsername) . ")</p>
    <p><strong>كلمة المرور:</strong> '<span style='background: lightblue; padding: 2px;'>$processedPassword</span>' (طول: " . strlen($processedPassword) . ")</p>
    </div>";
    
    // خطوة 2: فحص البيانات الفارغة
    if (empty($processedUsername) || empty($processedPassword)) {
        echo "<div class='alert alert-danger'>
        <h6>خطوة 2: فحص البيانات الفارغة</h6>
        <p>❌ البيانات فارغة!</p>
        <p>اسم المستخدم فارغ: " . (empty($processedUsername) ? 'نعم' : 'لا') . "</p>
        <p>كلمة المرور فارغة: " . (empty($processedPassword) ? 'نعم' : 'لا') . "</p>
        </div>";
    } else {
        echo "<div class='alert alert-success'>
        <h6>خطوة 2: فحص البيانات الفارغة</h6>
        <p>✅ البيانات غير فارغة</p>
        </div>";
        
        try {
            // خطوة 3: البحث في قاعدة البيانات
            echo "<div class='alert alert-info'>
            <h6>خطوة 3: البحث في قاعدة البيانات</h6>
            <p>الاستعلام: <code>SELECT * FROM users WHERE username = ? AND is_active = 1</code></p>
            <p>المعامل: '<code>$processedUsername</code>'</p>
            </div>";
            
            $stmt = $db->query("SELECT * FROM users WHERE username = ? AND is_active = 1", [$processedUsername]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user) {
                echo "<div class='alert alert-success'>
                <h6>خطوة 3: نتيجة البحث</h6>
                <p>✅ تم العثور على المستخدم!</p>
                <table class='table table-sm table-bordered mt-2'>
                <tr><td><strong>ID:</strong></td><td>{$user['id']}</td></tr>
                <tr><td><strong>اسم المستخدم:</strong></td><td><code>{$user['username']}</code></td></tr>
                <tr><td><strong>الدور:</strong></td><td>{$user['role']}</td></tr>
                <tr><td><strong>نشط:</strong></td><td>" . ($user['is_active'] ? 'نعم' : 'لا') . "</td></tr>
                <tr><td><strong>تاريخ الإنشاء:</strong></td><td>{$user['created_at']}</td></tr>
                </table>
                </div>";
                
                // خطوة 4: فحص كلمة المرور
                echo "<div class='alert alert-info'>
                <h6>خطوة 4: فحص كلمة المرور</h6>
                <p>كلمة المرور المدخلة: '<code>$processedPassword</code>'</p>
                <p>كلمة المرور المشفرة في قاعدة البيانات: <code>" . substr($user['password'], 0, 60) . "...</code></p>
                </div>";
                
                if (password_verify($processedPassword, $user['password'])) {
                    echo "<div class='alert alert-success'>
                    <h6>خطوة 4: نتيجة فحص كلمة المرور</h6>
                    <p>✅ كلمة المرور صحيحة!</p>
                    <h5 class='text-success mt-3'>🎉 تسجيل الدخول نجح!</h5>
                    <p>جميع الخطوات تمت بنجاح. المشكلة ليست في المنطق.</p>
                    <div class='mt-3'>
                    <a href='login.php' class='btn btn-success'>جرب تسجيل الدخول الحقيقي</a>
                    </div>
                    </div>";
                } else {
                    echo "<div class='alert alert-danger'>
                    <h6>خطوة 4: نتيجة فحص كلمة المرور</h6>
                    <p>❌ كلمة المرور خاطئة!</p>
                    </div>";
                    
                    // اختبار كلمات مرور مختلفة
                    echo "<div class='alert alert-warning'>
                    <h6>اختبار كلمات مرور مختلفة:</h6>";
                    
                    $testPasswords = [
                        $inputPassword,
                        trim($inputPassword),
                        'debug123',
                        'teacher123',
                        'test123'
                    ];
                    
                    $found = false;
                    foreach ($testPasswords as $testPass) {
                        if (password_verify($testPass, $user['password'])) {
                            echo "<p>✅ كلمة المرور الصحيحة: '<code>$testPass</code>'</p>";
                            $found = true;
                            break;
                        } else {
                            echo "<p>❌ ليست: '<code>$testPass</code>'</p>";
                        }
                    }
                    
                    if (!$found) {
                        echo "<p class='text-danger'>لم يتم العثور على كلمة المرور الصحيحة!</p>";
                    }
                    echo "</div>";
                }
                
            } else {
                echo "<div class='alert alert-danger'>
                <h6>خطوة 3: نتيجة البحث</h6>
                <p>❌ لم يتم العثور على المستخدم!</p>
                </div>";
                
                // البحث بدون شرط is_active
                $stmt = $db->query("SELECT * FROM users WHERE username = ?", [$processedUsername]);
                $userAny = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($userAny) {
                    echo "<div class='alert alert-warning'>
                    <h6>فحص إضافي: البحث بدون شرط النشاط</h6>
                    <p>⚠️ المستخدم موجود لكنه غير نشط!</p>
                    <p>is_active = {$userAny['is_active']}</p>
                    <p>سيتم تفعيله الآن...</p>
                    </div>";
                    
                    // تفعيل المستخدم
                    $stmt = $db->query("UPDATE users SET is_active = 1 WHERE username = ?", [$processedUsername]);
                    echo "<div class='alert alert-success'>تم تفعيل المستخدم! جرب تسجيل الدخول مرة أخرى.</div>";
                } else {
                    echo "<div class='alert alert-danger'>
                    <h6>فحص إضافي: البحث بدون شرط النشاط</h6>
                    <p>❌ المستخدم غير موجود نهائياً!</p>
                    </div>";
                    
                    // عرض أسماء المستخدمين المشابهة
                    $stmt = $db->query("SELECT username FROM users WHERE username LIKE ? LIMIT 5", ['%' . $processedUsername . '%']);
                    $similar = $stmt->fetchAll(PDO::FETCH_COLUMN);
                    
                    if (!empty($similar)) {
                        echo "<div class='alert alert-info'>
                        <h6>أسماء مستخدمين مشابهة:</h6>";
                        foreach ($similar as $sim) {
                            echo "<p>- '<code>$sim</code>'</p>";
                        }
                        echo "</div>";
                    }
                    
                    // عرض جميع المستخدمين
                    $stmt = $db->query("SELECT username, is_active FROM users WHERE role = 'teacher' ORDER BY created_at DESC LIMIT 10");
                    $allUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    if (!empty($allUsers)) {
                        echo "<div class='alert alert-info'>
                        <h6>آخر 10 معلمين في النظام:</h6>";
                        foreach ($allUsers as $u) {
                            $status = $u['is_active'] ? 'نشط' : 'غير نشط';
                            echo "<p>- '<code>{$u['username']}</code>' ($status)</p>";
                        }
                        echo "</div>";
                    }
                }
            }
            
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>
            <h6>خطأ في قاعدة البيانات:</h6>
            <p>" . $e->getMessage() . "</p>
            </div>";
        }
    }
}

// نموذج إنشاء مستخدم تجريبي
echo "<div class='row mb-4'>
<div class='col-md-6'>
<div class='card border-primary'>
<div class='card-header bg-primary text-white'>
<h6 class='mb-0'><i class='fas fa-plus me-2'></i>إنشاء مستخدم تجريبي</h6>
</div>
<div class='card-body'>
<p>أنشئ مستخدم تجريبي جديد لاختبار تسجيل الدخول</p>
<form method='POST'>
<button type='submit' name='create_test_user' class='btn btn-primary w-100'>
<i class='fas fa-user-plus me-2'></i>إنشاء مستخدم تجريبي
</button>
</form>
</div>
</div>
</div>

<div class='col-md-6'>
<div class='card border-success'>
<div class='card-header bg-success text-white'>
<h6 class='mb-0'><i class='fas fa-sign-in-alt me-2'></i>اختبار تسجيل الدخول</h6>
</div>
<div class='card-body'>
<form method='POST'>
<div class='mb-3'>
<label for='test_username' class='form-label'>اسم المستخدم:</label>
<input type='text' class='form-control' id='test_username' name='test_username' 
       value='" . ($_SESSION['debug_username'] ?? '') . "' required>
</div>
<div class='mb-3'>
<label for='test_password' class='form-label'>كلمة المرور:</label>
<input type='text' class='form-control' id='test_password' name='test_password' 
       value='" . ($_SESSION['debug_password'] ?? '') . "' required>
</div>
<button type='submit' name='test_login' class='btn btn-success w-100'>
<i class='fas fa-search me-2'></i>تشخيص تسجيل الدخول
</button>
</form>
</div>
</div>
</div>
</div>";

// عرض آخر المستخدمين
echo "<h5 class='text-primary'><i class='fas fa-users me-2'></i>المستخدمون الحاليون:</h5>";

try {
    $stmt = $db->query("SELECT u.*, t.first_name, t.last_name 
                       FROM users u 
                       LEFT JOIN teachers t ON u.id = t.user_id 
                       WHERE u.role = 'teacher' 
                       ORDER BY u.created_at DESC 
                       LIMIT 5");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($users)) {
        echo "<div class='table-responsive'>
        <table class='table table-striped table-sm'>
        <thead class='table-dark'>
        <tr>
        <th>اسم المستخدم</th>
        <th>اسم المعلم</th>
        <th>نشط</th>
        <th>تاريخ الإنشاء</th>
        <th>اختبار</th>
        </tr>
        </thead>
        <tbody>";
        
        foreach ($users as $user) {
            $teacherName = $user['first_name'] ? $user['first_name'] . ' ' . $user['last_name'] : 'غير محدد';
            $isActive = $user['is_active'] ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-danger">غير نشط</span>';
            $createdAt = date('Y-m-d H:i', strtotime($user['created_at']));
            
            echo "<tr>
            <td><code>{$user['username']}</code></td>
            <td>" . htmlspecialchars($teacherName) . "</td>
            <td>$isActive</td>
            <td>$createdAt</td>
            <td>
                <button class='btn btn-sm btn-outline-primary' onclick='testUser(\"{$user['username']}\")'>
                    اختبار
                </button>
            </td>
            </tr>";
        }
        
        echo "</tbody></table></div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>خطأ في جلب المستخدمين: " . $e->getMessage() . "</div>";
}

echo "<div class='alert alert-primary mt-4'>
<h5><i class='fas fa-info-circle me-2'></i>تعليمات:</h5>
<ol class='mb-0'>
<li><strong>أنشئ مستخدم تجريبي</strong> باستخدام الزر الأزرق</li>
<li><strong>انسخ البيانات</strong> التي ستظهر</li>
<li><strong>ضعها في نموذج الاختبار</strong> واضغط تشخيص</li>
<li><strong>تابع النتائج</strong> لمعرفة مكان المشكلة</li>
</ol>
</div>";

echo "</div>
</div>
</div>
</div>
</div>

<script>
function testUser(username) {
    document.getElementById('test_username').value = username;
    const password = prompt('أدخل كلمة المرور لهذا المستخدم:');
    if (password) {
        document.getElementById('test_password').value = password;
    }
}
</script>

</body>
</html>";
?>
