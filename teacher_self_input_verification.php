<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>التحقق من إدخال المعلمين لبياناتهم - نظام أرشفة الأساتذة</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap' rel='stylesheet'>
    <style>body { font-family: 'Cairo', sans-serif; }</style>
</head>
<body class='bg-light'>
<div class='container mt-5'>
<div class='row justify-content-center'>
<div class='col-md-12'>
<div class='card'>
<div class='card-header bg-success text-white'>
<h4 class='mb-0'><i class='fas fa-user-check me-2'></i>التحقق من قدرة المعلمين على إدخال بياناتهم بأنفسهم</h4>
</div>
<div class='card-body'>";

echo "<div class='alert alert-info'>
<h5><i class='fas fa-info-circle me-2'></i>النظام مصمم بحيث يقوم كل معلم بإدخال بياناته بنفسه</h5>
<p class='mb-0'>تم تصميم النظام بحيث يكون لكل معلم حساب منفصل يمكنه من خلاله إدخال وإدارة بياناته الشخصية دون تدخل من الإدارة.</p>
</div>";

// فحص الصفحات المتاحة للمعلمين
echo "<h5 class='text-primary'><i class='fas fa-check-circle me-2'></i>الصفحات المتاحة للمعلمين لإدخال بياناتهم:</h5>";

$teacherPages = [
    'preparations/my.php' => 'عرض التحضيرات اليومية',
    'preparations/add.php' => 'إضافة تحضير يومي جديد',
    'activities/my.php' => 'عرض الأنشطة والمسابقات',
    'activities/add.php' => 'إضافة نشاط أو مسابقة جديدة',
    'curriculum/my.php' => 'متابعة تقدم المنهج',
    'curriculum/add.php' => 'إضافة وحدة منهج جديدة',
    'files/my.php' => 'إدارة الملفات الشخصية',
    'profile.php' => 'تحديث الملف الشخصي'
];

foreach ($teacherPages as $page => $description) {
    if (file_exists($page)) {
        echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i><strong>$description</strong> - $page متوفر</div>";
    } else {
        echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i><strong>$description</strong> - $page غير متوفر</div>";
    }
}

// فحص آلية الحماية في الصفحات
echo "<h5 class='text-primary mt-4'><i class='fas fa-shield-alt me-2'></i>فحص آلية الحماية والتحقق من الصلاحيات:</h5>";

$protectionChecks = [
    'preparations/add.php' => 'التحضير اليومي',
    'activities/add.php' => 'الأنشطة والمسابقات',
    'curriculum/add.php' => 'وحدات المنهج'
];

foreach ($protectionChecks as $file => $type) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        
        // فحص وجود التحقق من تسجيل الدخول
        if (strpos($content, 'isLoggedIn()') !== false && strpos($content, 'isTeacher()') !== false) {
            echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>صفحة $type محمية بالتحقق من تسجيل الدخول وصلاحيات المعلم</div>";
        } else {
            echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle me-2'></i>صفحة $type قد تحتاج لحماية إضافية</div>";
        }
        
        // فحص ربط البيانات بالمعلم الحالي
        if (strpos($content, 'currentTeacher') !== false && strpos($content, 'teacher_id') !== false) {
            echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>صفحة $type تربط البيانات بالمعلم الحالي المسجل</div>";
        } else {
            echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle me-2'></i>صفحة $type قد لا تربط البيانات بالمعلم بشكل صحيح</div>";
        }
    }
}

// فحص قاعدة البيانات للتأكد من ربط البيانات بالمعلمين
echo "<h5 class='text-primary mt-4'><i class='fas fa-database me-2'></i>فحص ربط البيانات في قاعدة البيانات:</h5>";

try {
    $tables = ['daily_preparations', 'activities', 'curriculum_progress', 'files'];
    
    foreach ($tables as $table) {
        $stmt = $db->query("PRAGMA table_info($table)");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $hasTeacherId = false;
        foreach ($columns as $column) {
            if ($column['name'] === 'teacher_id') {
                $hasTeacherId = true;
                break;
            }
        }
        
        if ($hasTeacherId) {
            echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>جدول $table يحتوي على عمود teacher_id لربط البيانات بالمعلم</div>";
            
            // فحص وجود بيانات مرتبطة بالمعلمين
            $stmt = $db->query("SELECT COUNT(*) FROM $table WHERE teacher_id IS NOT NULL");
            $count = $stmt->fetchColumn();
            echo "<div class='alert alert-info'><i class='fas fa-info me-2'></i>جدول $table يحتوي على $count سجل مرتبط بالمعلمين</div>";
        } else {
            echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>جدول $table لا يحتوي على عمود teacher_id</div>";
        }
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-exclamation-triangle me-2'></i>خطأ في فحص قاعدة البيانات: " . $e->getMessage() . "</div>";
}

// عرض مثال على كيفية عمل النظام
echo "<h5 class='text-primary mt-4'><i class='fas fa-user-cog me-2'></i>كيفية عمل النظام للمعلمين:</h5>";

echo "<div class='card'>
<div class='card-body'>
<h6 class='card-title text-success'><i class='fas fa-check-circle me-2'></i>خطوات المعلم لإدخال بياناته:</h6>
<ol class='list-group list-group-numbered'>
<li class='list-group-item d-flex justify-content-between align-items-start'>
<div class='ms-2 me-auto'>
<div class='fw-bold'>تسجيل الدخول</div>
المعلم يسجل دخوله بحسابه الشخصي
</div>
<span class='badge bg-primary rounded-pill'>1</span>
</li>
<li class='list-group-item d-flex justify-content-between align-items-start'>
<div class='ms-2 me-auto'>
<div class='fw-bold'>الوصول للصفحة الرئيسية</div>
يرى لوحة تحكم شخصية تعرض إحصائياته
</div>
<span class='badge bg-primary rounded-pill'>2</span>
</li>
<li class='list-group-item d-flex justify-content-between align-items-start'>
<div class='ms-2 me-auto'>
<div class='fw-bold'>إضافة التحضير اليومي</div>
يذهب لصفحة \"تحضيري اليومي\" ويضيف تحضيرات جديدة
</div>
<span class='badge bg-primary rounded-pill'>3</span>
</li>
<li class='list-group-item d-flex justify-content-between align-items-start'>
<div class='ms-2 me-auto'>
<div class='fw-bold'>إدارة الأنشطة</div>
يضيف ويدير أنشطته ومسابقاته الخاصة
</div>
<span class='badge bg-primary rounded-pill'>4</span>
</li>
<li class='list-group-item d-flex justify-content-between align-items-start'>
<div class='ms-2 me-auto'>
<div class='fw-bold'>متابعة المنهج</div>
يسجل تقدمه في وحدات المنهج المختلفة
</div>
<span class='badge bg-primary rounded-pill'>5</span>
</li>
<li class='list-group-item d-flex justify-content-between align-items-start'>
<div class='ms-2 me-auto'>
<div class='fw-bold'>رفع الملفات</div>
يرفع ملفاته الشخصية والوثائق المطلوبة
</div>
<span class='badge bg-primary rounded-pill'>6</span>
</li>
</ol>
</div>
</div>";

// النتيجة النهائية
echo "<div class='mt-4'>
<div class='alert alert-success alert-lg'>
<h4><i class='fas fa-check-circle me-2'></i>النظام يعمل بالطريقة المطلوبة!</h4>
<hr>
<p class='mb-0'>تم التأكد من أن النظام مصمم بحيث يقوم كل معلم بإدخال بياناته بنفسه. جميع الصفحات محمية بالتحقق من الصلاحيات وتربط البيانات بالمعلم المسجل حالياً.</p>
</div>
</div>";

// روابط سريعة للاختبار
echo "<div class='alert alert-primary mt-4'>
<h5><i class='fas fa-link me-2'></i>روابط سريعة لاختبار النظام كمعلم:</h5>
<div class='row'>
<div class='col-md-6'>
<ul class='list-unstyled'>
<li><a href='login.php' class='btn btn-sm btn-outline-primary me-2 mb-2'><i class='fas fa-sign-in-alt me-1'></i>تسجيل الدخول</a></li>
<li><a href='preparations/my.php' class='btn btn-sm btn-outline-success me-2 mb-2'><i class='fas fa-book me-1'></i>تحضيري اليومي</a></li>
<li><a href='activities/my.php' class='btn btn-sm btn-outline-warning me-2 mb-2'><i class='fas fa-trophy me-1'></i>أنشطتي</a></li>
</ul>
</div>
<div class='col-md-6'>
<ul class='list-unstyled'>
<li><a href='curriculum/my.php' class='btn btn-sm btn-outline-info me-2 mb-2'><i class='fas fa-tasks me-1'></i>متابعة المنهج</a></li>
<li><a href='files/my.php' class='btn btn-sm btn-outline-secondary me-2 mb-2'><i class='fas fa-folder me-1'></i>ملفاتي</a></li>
<li><a href='profile.php' class='btn btn-sm btn-outline-dark me-2 mb-2'><i class='fas fa-user me-1'></i>ملفي الشخصي</a></li>
</ul>
</div>
</div>
</div>";

echo "</div>
</div>
</div>
</div>
</div>
</body>
</html>";
?>
