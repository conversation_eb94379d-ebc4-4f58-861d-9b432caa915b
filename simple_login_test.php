<?php
require_once 'config/config.php';

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

$error = '';
$success = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    echo "<div style='background: #f8f9fa; padding: 10px; margin: 10px; border: 1px solid #ddd;'>";
    echo "<h5>تشخيص مباشر:</h5>";
    echo "<p><strong>اسم المستخدم المدخل:</strong> '$username' (طول: " . strlen($username) . ")</p>";
    echo "<p><strong>كلمة المرور المدخلة:</strong> '$password' (طول: " . strlen($password) . ")</p>";
    
    if (empty($username) || empty($password)) {
        $error = 'يرجى إدخال اسم المستخدم وكلمة المرور';
        echo "<p style='color: red;'>❌ البيانات فارغة</p>";
    } else {
        echo "<p style='color: green;'>✅ البيانات غير فارغة</p>";
        
        try {
            echo "<p><strong>الاستعلام:</strong> SELECT * FROM users WHERE username = '$username' AND is_active = 1</p>";
            
            $stmt = $db->query("SELECT * FROM users WHERE username = ? AND is_active = 1", [$username]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user) {
                echo "<p style='color: green;'>✅ تم العثور على المستخدم: ID = {$user['id']}</p>";
                echo "<p><strong>اسم المستخدم في قاعدة البيانات:</strong> '{$user['username']}'</p>";
                echo "<p><strong>نشط:</strong> " . ($user['is_active'] ? 'نعم' : 'لا') . "</p>";
                
                if (password_verify($password, $user['password'])) {
                    echo "<p style='color: green;'>✅ كلمة المرور صحيحة!</p>";
                    
                    // تسجيل الدخول بنجاح
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['role'] = $user['role'];
                    $_SESSION['login_time'] = time();
                    
                    $success = 'تم تسجيل الدخول بنجاح!';
                    echo "<p style='color: green; font-weight: bold;'>🎉 تسجيل الدخول نجح!</p>";
                    echo "<p><a href='index.php' style='background: green; color: white; padding: 10px; text-decoration: none;'>الذهاب للصفحة الرئيسية</a></p>";
                    
                } else {
                    echo "<p style='color: red;'>❌ كلمة المرور خاطئة!</p>";
                    $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
                    
                    // اختبار كلمات مرور مختلفة
                    echo "<p><strong>اختبار كلمات مرور مختلفة:</strong></p>";
                    $testPasswords = ['debug123', 'teacher123', 'test123', trim($password)];
                    foreach ($testPasswords as $testPass) {
                        if (password_verify($testPass, $user['password'])) {
                            echo "<p style='color: green;'>✅ كلمة المرور الصحيحة: '$testPass'</p>";
                            break;
                        } else {
                            echo "<p style='color: red;'>❌ ليست: '$testPass'</p>";
                        }
                    }
                }
            } else {
                echo "<p style='color: red;'>❌ لم يتم العثور على المستخدم!</p>";
                $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
                
                // البحث بدون شرط is_active
                $stmt = $db->query("SELECT * FROM users WHERE username = ?", [$username]);
                $userAny = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($userAny) {
                    echo "<p style='color: orange;'>⚠️ المستخدم موجود لكنه غير نشط (is_active = {$userAny['is_active']})</p>";
                    echo "<p>سيتم تفعيله الآن...</p>";
                    
                    $stmt = $db->query("UPDATE users SET is_active = 1 WHERE username = ?", [$username]);
                    echo "<p style='color: green;'>✅ تم تفعيل المستخدم! جرب مرة أخرى.</p>";
                } else {
                    echo "<p style='color: red;'>❌ المستخدم غير موجود نهائياً!</p>";
                    
                    // عرض المستخدمين المتاحين
                    $stmt = $db->query("SELECT username FROM users WHERE role = 'teacher' LIMIT 5");
                    $availableUsers = $stmt->fetchAll(PDO::FETCH_COLUMN);
                    
                    if (!empty($availableUsers)) {
                        echo "<p><strong>المستخدمون المتاحون:</strong></p>";
                        foreach ($availableUsers as $availableUser) {
                            echo "<p>- '$availableUser'</p>";
                        }
                    }
                }
            }
            
        } catch (Exception $e) {
            $error = 'خطأ في النظام: ' . $e->getMessage();
            echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
        }
    }
    echo "</div>";
}

// إنشاء مستخدم تجريبي سريع
if (isset($_GET['create_test'])) {
    $testUsername = 'quicktest' . time();
    $testPassword = 'quick123';
    
    try {
        $db->getConnection()->beginTransaction();
        
        $hashedPassword = password_hash($testPassword, PASSWORD_DEFAULT);
        $stmt = $db->query("INSERT INTO users (username, password, role, is_active) VALUES (?, ?, 'teacher', 1)", 
                          [$testUsername, $hashedPassword]);
        $userId = $db->lastInsertId();
        
        $stmt = $db->query("INSERT INTO teachers (user_id, employee_id, first_name, last_name, email, subject, grade_level, hire_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", 
                          [$userId, 'QUICK' . time(), 'معلم', 'سريع', '<EMAIL>', 'اختبار', 'الصف الأول', date('Y-m-d')]);
        
        $db->getConnection()->commit();
        
        echo "<div style='background: #d4edda; padding: 15px; margin: 10px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
        echo "<h4>تم إنشاء مستخدم تجريبي!</h4>";
        echo "<p><strong>اسم المستخدم:</strong> <code>$testUsername</code></p>";
        echo "<p><strong>كلمة المرور:</strong> <code>$testPassword</code></p>";
        echo "<p>استخدم هذه البيانات في النموذج أدناه.</p>";
        echo "</div>";
        
        // ملء النموذج تلقائياً
        $_POST['username'] = $testUsername;
        $_POST['password'] = $testPassword;
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; margin: 10px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
        echo "<p>خطأ في إنشاء المستخدم: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول المبسط</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>body { font-family: 'Cairo', sans-serif; }</style>
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">اختبار تسجيل الدخول المبسط</h4>
                    </div>
                    <div class="card-body">
                        
                        <?php if (!empty($error)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo $error; ?>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($success)): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check me-2"></i>
                            <?php echo $success; ?>
                        </div>
                        <?php endif; ?>
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="username" class="form-label">اسم المستخدم:</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" 
                                       required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">كلمة المرور:</label>
                                <input type="text" class="form-control" id="password" name="password" 
                                       value="<?php echo htmlspecialchars($_POST['password'] ?? ''); ?>" 
                                       required>
                                <small class="text-muted">نوع النص مكشوف للاختبار</small>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100">
                                تسجيل الدخول
                            </button>
                        </form>
                        
                        <hr>
                        
                        <div class="text-center">
                            <a href="?create_test=1" class="btn btn-success">
                                إنشاء مستخدم تجريبي سريع
                            </a>
                            <a href="login_debug.php" class="btn btn-warning">
                                تشخيص مفصل
                            </a>
                        </div>
                        
                        <hr>
                        
                        <!-- عرض المستخدمين المتاحين -->
                        <h6>المستخدمون المتاحون:</h6>
                        <?php
                        try {
                            $stmt = $db->query("SELECT username, is_active FROM users WHERE role = 'teacher' ORDER BY created_at DESC LIMIT 5");
                            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
                            
                            if (!empty($users)) {
                                echo "<div class='table-responsive'>";
                                echo "<table class='table table-sm'>";
                                echo "<thead><tr><th>اسم المستخدم</th><th>نشط</th><th>اختبار</th></tr></thead>";
                                echo "<tbody>";
                                
                                foreach ($users as $user) {
                                    $status = $user['is_active'] ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-danger">غير نشط</span>';
                                    echo "<tr>";
                                    echo "<td><code>{$user['username']}</code></td>";
                                    echo "<td>$status</td>";
                                    echo "<td><button class='btn btn-sm btn-outline-primary' onclick='fillForm(\"{$user['username']}\")'>استخدام</button></td>";
                                    echo "</tr>";
                                }
                                
                                echo "</tbody></table>";
                                echo "</div>";
                            }
                        } catch (Exception $e) {
                            echo "<p class='text-danger'>خطأ في جلب المستخدمين: " . $e->getMessage() . "</p>";
                        }
                        ?>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function fillForm(username) {
            document.getElementById('username').value = username;
            const password = prompt('أدخل كلمة المرور لهذا المستخدم:');
            if (password) {
                document.getElementById('password').value = password;
            }
        }
    </script>
</body>
</html>
