<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام أرشفة الأساتذة - اختبار</title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-school me-2"></i>
                نظام أرشفة الأساتذة
            </a>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="page-header">
                    <div class="container">
                        <h1 class="page-title">
                            <i class="fas fa-check-circle me-3"></i>
                            نظام أرشفة الأساتذة جاهز!
                        </h1>
                        <p class="page-subtitle">تم إنشاء النظام بنجاح - يحتاج إلى خادم PHP للعمل</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات النظام -->
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات النظام
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check me-2"></i>تم إنشاء النظام بنجاح!</h6>
                            <p class="mb-0">جميع الملفات جاهزة ومنظمة بشكل صحيح.</p>
                        </div>
                        
                        <h6 class="text-primary mb-3">المتطلبات للتشغيل:</h6>
                        <ul class="list-group list-group-flush mb-4">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fab fa-php me-2"></i>PHP 7.4 أو أحدث</span>
                                <span class="badge bg-warning">مطلوب</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-database me-2"></i>SQLite3</span>
                                <span class="badge bg-warning">مطلوب</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-server me-2"></i>خادم ويب (Apache/Nginx/PHP Built-in)</span>
                                <span class="badge bg-warning">مطلوب</span>
                            </li>
                        </ul>

                        <h6 class="text-primary mb-3">خطوات التشغيل:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-download me-2"></i>
                                            تثبيت PHP
                                        </h6>
                                        <ol class="small">
                                            <li>قم بتحميل PHP من <a href="https://www.php.net/downloads" target="_blank">php.net</a></li>
                                            <li>قم بتثبيته وإضافته إلى PATH</li>
                                            <li>تأكد من تفعيل SQLite</li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-play me-2"></i>
                                            تشغيل النظام
                                        </h6>
                                        <ol class="small">
                                            <li>افتح Terminal/CMD</li>
                                            <li>انتقل لمجلد المشروع</li>
                                            <li>شغل: <code>php -S localhost:8000</code></li>
                                            <li>افتح: <code>localhost:8000</code></li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info mt-4">
                            <h6><i class="fas fa-lightbulb me-2"></i>بديل سريع:</h6>
                            <p class="mb-2">يمكنك استخدام XAMPP أو WAMP أو MAMP لتشغيل النظام بسهولة:</p>
                            <ul class="mb-0">
                                <li>قم بتحميل وتثبيت XAMPP</li>
                                <li>انسخ مجلد المشروع إلى htdocs</li>
                                <li>شغل Apache من لوحة تحكم XAMPP</li>
                                <li>افتح localhost/اسم_المجلد</li>
                            </ul>
                        </div>

                        <h6 class="text-primary mb-3">بيانات تسجيل الدخول:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <i class="fas fa-user-shield me-2"></i>
                                        المدير
                                    </div>
                                    <div class="card-body">
                                        <p class="mb-1"><strong>اسم المستخدم:</strong> admin</p>
                                        <p class="mb-0"><strong>كلمة المرور:</strong> admin123</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <i class="fas fa-chalkboard-teacher me-2"></i>
                                        المعلم
                                    </div>
                                    <div class="card-body">
                                        <p class="mb-1"><strong>اسم المستخدم:</strong> teacher1</p>
                                        <p class="mb-0"><strong>كلمة المرور:</strong> teacher123</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h6 class="text-primary mb-3 mt-4">الملفات المُنشأة:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-unstyled small">
                                    <li><i class="fas fa-file-code text-primary me-2"></i>index.php - الصفحة الرئيسية</li>
                                    <li><i class="fas fa-file-code text-success me-2"></i>login.php - تسجيل الدخول</li>
                                    <li><i class="fas fa-file-code text-warning me-2"></i>profile.php - الملف الشخصي</li>
                                    <li><i class="fas fa-folder text-info me-2"></i>teachers/ - إدارة المعلمين</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled small">
                                    <li><i class="fas fa-folder text-secondary me-2"></i>config/ - الإعدادات</li>
                                    <li><i class="fas fa-folder text-primary me-2"></i>assets/ - CSS & JS</li>
                                    <li><i class="fas fa-folder text-success me-2"></i>ajax/ - طلبات AJAX</li>
                                    <li><i class="fas fa-file text-danger me-2"></i>setup_demo_data.php - البيانات التجريبية</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- روابط مفيدة -->
        <div class="row mt-4">
            <div class="col-lg-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-link me-2"></i>
                            روابط مفيدة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 text-center mb-3">
                                <a href="https://www.php.net/downloads" target="_blank" class="btn btn-outline-primary">
                                    <i class="fab fa-php fa-2x d-block mb-2"></i>
                                    تحميل PHP
                                </a>
                            </div>
                            <div class="col-md-4 text-center mb-3">
                                <a href="https://www.apachefriends.org/download.html" target="_blank" class="btn btn-outline-success">
                                    <i class="fas fa-server fa-2x d-block mb-2"></i>
                                    تحميل XAMPP
                                </a>
                            </div>
                            <div class="col-md-4 text-center mb-3">
                                <a href="README.md" class="btn btn-outline-info">
                                    <i class="fas fa-book fa-2x d-block mb-2"></i>
                                    دليل المستخدم
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light mt-5 py-4">
        <div class="container text-center">
            <p class="text-muted mb-0">
                <i class="fas fa-heart text-danger me-1"></i>
                تم تطويره بـ ❤️ لخدمة التعليم
            </p>
        </div>
    </footer>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
