<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>التحقق من أزرار الإضافة للمعلمين - نظام أرشفة الأساتذة</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap' rel='stylesheet'>
    <style>body { font-family: 'Cairo', sans-serif; }</style>
</head>
<body class='bg-light'>
<div class='container mt-5'>
<div class='row justify-content-center'>
<div class='col-md-10'>
<div class='card'>
<div class='card-header bg-success text-white'>
<h4 class='mb-0'><i class='fas fa-check-circle me-2'></i>التحقق من أزرار الإضافة للمعلمين</h4>
</div>
<div class='card-body'>";

echo "<div class='alert alert-info'>
<h5><i class='fas fa-info-circle me-2'></i>تم إضافة أزرار الإضافة السريعة للمعلمين</h5>
<p class='mb-0'>الآن يمكن للمعلمين الوصول بسهولة لإضافة محتوى جديد من الصفحة الرئيسية.</p>
</div>";

// فحص الصفحات المطلوبة للإضافة
echo "<h5 class='text-primary'><i class='fas fa-plus-circle me-2'></i>صفحات الإضافة المتاحة للمعلمين:</h5>";

$addPages = [
    'preparations/add.php' => 'إضافة تحضير يومي جديد',
    'activities/add.php' => 'إضافة نشاط أو مسابقة جديدة',
    'curriculum/add.php' => 'إضافة وحدة منهج جديدة',
    'files/my.php' => 'رفع ملف جديد (في نفس الصفحة)'
];

foreach ($addPages as $page => $description) {
    if (file_exists($page)) {
        echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i><strong>$description</strong> - $page متوفر</div>";
    } else {
        echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i><strong>$description</strong> - $page غير متوفر</div>";
    }
}

// فحص الأزرار في الصفحة الرئيسية
echo "<h5 class='text-primary mt-4'><i class='fas fa-mouse-pointer me-2'></i>الأزرار المضافة في الصفحة الرئيسية:</h5>";

if (file_exists('index.php')) {
    $indexContent = file_get_contents('index.php');
    
    $buttonsToCheck = [
        'إضافة تحضير جديد' => 'preparations/add.php',
        'إضافة نشاط جديد' => 'activities/add.php',
        'إضافة وحدة جديدة' => 'curriculum/add.php',
        'رفع ملف جديد' => 'files/my.php'
    ];
    
    foreach ($buttonsToCheck as $buttonText => $link) {
        if (strpos($indexContent, $buttonText) !== false && strpos($indexContent, $link) !== false) {
            echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>زر \"$buttonText\" موجود ويؤدي إلى $link</div>";
        } else {
            echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle me-2'></i>زر \"$buttonText\" غير موجود أو لا يؤدي للرابط الصحيح</div>";
        }
    }
}

// عرض مثال على الواجهة الجديدة
echo "<h5 class='text-primary mt-4'><i class='fas fa-desktop me-2'></i>الواجهة الجديدة للمعلمين:</h5>";

echo "<div class='card'>
<div class='card-body'>
<h6 class='card-title text-success'><i class='fas fa-user-check me-2'></i>ما يراه المعلم الآن في الصفحة الرئيسية:</h6>

<div class='row'>
<div class='col-md-6'>
<div class='card border-primary'>
<div class='card-header bg-primary text-white text-center'>
<h6 class='mb-0'><i class='fas fa-book me-2'></i>التحضير اليومي</h6>
</div>
<div class='card-body text-center'>
<h4 class='text-primary'>عدد التحضيرات</h4>
<div class='d-grid gap-2'>
<button class='btn btn-primary'>عرض تحضيراتي</button>
<button class='btn btn-outline-primary'><i class='fas fa-plus me-2'></i>إضافة تحضير جديد</button>
</div>
</div>
</div>
</div>

<div class='col-md-6'>
<div class='card border-success'>
<div class='card-header bg-success text-white text-center'>
<h6 class='mb-0'><i class='fas fa-trophy me-2'></i>أنشطتي</h6>
</div>
<div class='card-body text-center'>
<h4 class='text-success'>عدد الأنشطة</h4>
<div class='d-grid gap-2'>
<button class='btn btn-success'>عرض الكل</button>
<button class='btn btn-outline-success'><i class='fas fa-plus me-2'></i>إضافة نشاط جديد</button>
</div>
</div>
</div>
</div>
</div>

<div class='row mt-3'>
<div class='col-md-4'>
<div class='card border-info'>
<div class='card-header bg-info text-white text-center'>
<h6 class='mb-0'><i class='fas fa-tasks me-2'></i>متابعة المنهج</h6>
</div>
<div class='card-body text-center'>
<h4 class='text-info'>عدد الوحدات</h4>
<div class='d-grid gap-2'>
<button class='btn btn-info'>عرض التقدم</button>
<button class='btn btn-outline-info'><i class='fas fa-plus me-2'></i>إضافة وحدة جديدة</button>
</div>
</div>
</div>
</div>

<div class='col-md-4'>
<div class='card border-secondary'>
<div class='card-header bg-secondary text-white text-center'>
<h6 class='mb-0'><i class='fas fa-folder me-2'></i>ملفاتي</h6>
</div>
<div class='card-body text-center'>
<h4 class='text-secondary'>عدد الملفات</h4>
<div class='d-grid gap-2'>
<button class='btn btn-secondary'>عرض الملفات</button>
<button class='btn btn-outline-secondary'><i class='fas fa-upload me-2'></i>رفع ملف جديد</button>
</div>
</div>
</div>
</div>

<div class='col-md-4'>
<div class='card border-dark'>
<div class='card-header bg-dark text-white text-center'>
<h6 class='mb-0'><i class='fas fa-user-circle me-2'></i>ملفي الشخصي</h6>
</div>
<div class='card-body text-center'>
<p class='text-muted'>إدارة البيانات الشخصية</p>
<div class='d-grid gap-2'>
<button class='btn btn-dark'>عرض الملف</button>
<button class='btn btn-outline-dark'><i class='fas fa-edit me-2'></i>تحديث البيانات</button>
</div>
</div>
</div>
</div>
</div>

</div>
</div>";

// النتيجة النهائية
echo "<div class='mt-4'>
<div class='alert alert-success alert-lg'>
<h4><i class='fas fa-check-circle me-2'></i>تم حل المشكلة بنجاح!</h4>
<hr>
<p class='mb-0'>الآن يمكن للمعلمين الوصول بسهولة لإضافة محتوى جديد من الصفحة الرئيسية. تم إضافة أزرار الإضافة السريعة لجميع الأقسام.</p>
</div>
</div>";

// خطوات للمعلم
echo "<div class='alert alert-primary mt-4'>
<h5><i class='fas fa-user-graduate me-2'></i>خطوات المعلم لإضافة محتوى جديد:</h5>
<ol class='mb-0'>
<li><strong>تسجيل الدخول:</strong> باستخدام حساب المعلم</li>
<li><strong>الذهاب للصفحة الرئيسية:</strong> سيرى لوحة تحكم شخصية</li>
<li><strong>اختيار نوع المحتوى:</strong> تحضير، نشاط، وحدة منهج، أو ملف</li>
<li><strong>الضغط على زر الإضافة:</strong> الزر الأخضر مع علامة +</li>
<li><strong>ملء النموذج:</strong> إدخال البيانات المطلوبة</li>
<li><strong>الحفظ:</strong> الضغط على زر الحفظ</li>
</ol>
</div>";

// روابط سريعة للاختبار
echo "<div class='alert alert-warning mt-4'>
<h5><i class='fas fa-link me-2'></i>روابط سريعة لاختبار الإضافة:</h5>
<div class='row'>
<div class='col-md-6'>
<ul class='list-unstyled'>
<li><a href='login.php' class='btn btn-sm btn-outline-primary me-2 mb-2'><i class='fas fa-sign-in-alt me-1'></i>تسجيل الدخول كمعلم</a></li>
<li><a href='preparations/add.php' class='btn btn-sm btn-outline-success me-2 mb-2'><i class='fas fa-plus me-1'></i>إضافة تحضير</a></li>
<li><a href='activities/add.php' class='btn btn-sm btn-outline-warning me-2 mb-2'><i class='fas fa-plus me-1'></i>إضافة نشاط</a></li>
</ul>
</div>
<div class='col-md-6'>
<ul class='list-unstyled'>
<li><a href='curriculum/add.php' class='btn btn-sm btn-outline-info me-2 mb-2'><i class='fas fa-plus me-1'></i>إضافة وحدة منهج</a></li>
<li><a href='files/my.php' class='btn btn-sm btn-outline-secondary me-2 mb-2'><i class='fas fa-upload me-1'></i>رفع ملف</a></li>
<li><a href='index.php' class='btn btn-sm btn-outline-dark me-2 mb-2'><i class='fas fa-home me-1'></i>الصفحة الرئيسية</a></li>
</ul>
</div>
</div>
</div>";

echo "</div>
</div>
</div>
</div>
</div>
</body>
</html>";
?>
