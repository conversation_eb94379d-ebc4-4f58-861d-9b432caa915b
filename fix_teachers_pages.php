<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إصلاح صفحات المعلمين - نظام أرشفة الأساتذة</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap' rel='stylesheet'>
    <style>body { font-family: 'Cairo', sans-serif; }</style>
</head>
<body class='bg-light'>
<div class='container mt-5'>
<div class='row justify-content-center'>
<div class='col-md-10'>
<div class='card'>
<div class='card-header bg-success text-white'>
<h4 class='mb-0'><i class='fas fa-check-circle me-2'></i>إصلاح صفحات المعلمين</h4>
</div>
<div class='card-body'>";

echo "<div class='alert alert-info'>
<h5><i class='fas fa-info-circle me-2'></i>تم إصلاح مشكلة صفحات المعلمين</h5>
<p class='mb-0'>تم إنشاء جميع الصفحات المطلوبة لإدارة المعلمين.</p>
</div>";

// فحص الصفحات المطلوبة
echo "<h5 class='text-primary'><i class='fas fa-check-circle me-2'></i>الصفحات المتاحة الآن:</h5>";

$teacherPages = [
    'teachers/list.php' => 'قائمة المعلمين',
    'teachers/add.php' => 'إضافة معلم جديد',
    'teachers/view.php' => 'عرض تفاصيل المعلم',
    'teachers/edit.php' => 'تعديل بيانات المعلم',
    'teachers/delete.php' => 'حذف المعلم'
];

foreach ($teacherPages as $page => $description) {
    if (file_exists($page)) {
        echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i><strong>$description</strong> - $page متوفر</div>";
    } else {
        echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i><strong>$description</strong> - $page غير متوفر</div>";
    }
}

// فحص الروابط في قائمة المعلمين
echo "<h5 class='text-primary mt-4'><i class='fas fa-link me-2'></i>فحص الروابط في قائمة المعلمين:</h5>";

if (file_exists('teachers/list.php')) {
    $listContent = file_get_contents('teachers/list.php');
    
    $linksToCheck = [
        'view.php?id=' => 'رابط عرض التفاصيل',
        'edit.php?id=' => 'رابط تعديل البيانات',
        'delete.php?id=' => 'رابط حذف المعلم'
    ];
    
    foreach ($linksToCheck as $link => $description) {
        if (strpos($listContent, $link) !== false) {
            echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>$description موجود</div>";
        } else {
            echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle me-2'></i>$description غير موجود</div>";
        }
    }
}

// عرض إحصائيات المعلمين
echo "<h5 class='text-primary mt-4'><i class='fas fa-chart-bar me-2'></i>إحصائيات المعلمين:</h5>";

try {
    $totalTeachers = $db->query("SELECT COUNT(*) FROM teachers")->fetchColumn();
    $teachersWithAccounts = $db->query("SELECT COUNT(*) FROM teachers t JOIN users u ON t.user_id = u.id")->fetchColumn();
    $teachersWithoutAccounts = $totalTeachers - $teachersWithAccounts;
    $activeTeachers = $db->query("SELECT COUNT(*) FROM teachers t JOIN users u ON t.user_id = u.id WHERE u.is_active = 1")->fetchColumn();
    
    echo "<div class='row'>
    <div class='col-md-3'>
        <div class='card text-center'>
            <div class='card-body'>
                <h4 class='text-primary'>$totalTeachers</h4>
                <p class='mb-0'>إجمالي المعلمين</p>
            </div>
        </div>
    </div>
    <div class='col-md-3'>
        <div class='card text-center'>
            <div class='card-body'>
                <h4 class='text-success'>$teachersWithAccounts</h4>
                <p class='mb-0'>لديهم حسابات</p>
            </div>
        </div>
    </div>
    <div class='col-md-3'>
        <div class='card text-center'>
            <div class='card-body'>
                <h4 class='text-warning'>$teachersWithoutAccounts</h4>
                <p class='mb-0'>بدون حسابات</p>
            </div>
        </div>
    </div>
    <div class='col-md-3'>
        <div class='card text-center'>
            <div class='card-body'>
                <h4 class='text-info'>$activeTeachers</h4>
                <p class='mb-0'>حسابات نشطة</p>
            </div>
        </div>
    </div>
    </div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ في جلب الإحصائيات: " . $e->getMessage() . "</div>";
}

// عرض قائمة المعلمين مع الروابط
echo "<h5 class='text-primary mt-4'><i class='fas fa-users me-2'></i>قائمة المعلمين مع الروابط:</h5>";

try {
    $stmt = $db->query("SELECT t.*, u.username, u.is_active 
                       FROM teachers t 
                       LEFT JOIN users u ON t.user_id = u.id 
                       ORDER BY t.first_name, t.last_name 
                       LIMIT 10");
    $teachers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($teachers)) {
        echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle me-2'></i>لا يوجد معلمون في النظام</div>";
    } else {
        echo "<div class='table-responsive'>
        <table class='table table-striped table-sm'>
        <thead class='table-dark'>
        <tr>
        <th>الاسم</th>
        <th>رقم الموظف</th>
        <th>المادة</th>
        <th>الصف</th>
        <th>حساب المستخدم</th>
        <th>الإجراءات</th>
        </tr>
        </thead>
        <tbody>";
        
        foreach ($teachers as $teacher) {
            $teacherName = $teacher['first_name'] . ' ' . $teacher['last_name'];
            $accountStatus = $teacher['username'] ? 
                ($teacher['is_active'] ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-warning">غير نشط</span>') : 
                '<span class="badge bg-danger">لا يوجد</span>';
            
            echo "<tr>
            <td><strong>" . htmlspecialchars($teacherName) . "</strong></td>
            <td>" . htmlspecialchars($teacher['employee_id']) . "</td>
            <td>" . htmlspecialchars($teacher['subject']) . "</td>
            <td>" . htmlspecialchars($teacher['grade_level']) . "</td>
            <td>$accountStatus</td>
            <td>
                <a href='teachers/view.php?id={$teacher['id']}' class='btn btn-sm btn-outline-primary' title='عرض'>
                    <i class='fas fa-eye'></i>
                </a>
                <a href='teachers/edit.php?id={$teacher['id']}' class='btn btn-sm btn-outline-warning' title='تعديل'>
                    <i class='fas fa-edit'></i>
                </a>
                <button class='btn btn-sm btn-outline-danger' onclick='confirmDelete({$teacher['id']}, \"$teacherName\")' title='حذف'>
                    <i class='fas fa-trash'></i>
                </button>
            </td>
            </tr>";
        }
        
        echo "</tbody></table></div>";
        
        if ($totalTeachers > 10) {
            echo "<div class='text-center mt-3'>
            <a href='teachers/list.php' class='btn btn-primary'>عرض جميع المعلمين ($totalTeachers)</a>
            </div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ في جلب قائمة المعلمين: " . $e->getMessage() . "</div>";
}

// النتيجة النهائية
echo "<div class='mt-4'>
<div class='alert alert-success alert-lg'>
<h4><i class='fas fa-check-circle me-2'></i>تم إصلاح المشكلة بنجاح!</h4>
<hr>
<p class='mb-0'>الآن يمكن الوصول لجميع صفحات إدارة المعلمين بدون مشاكل. جميع الروابط تعمل بشكل صحيح.</p>
</div>
</div>";

// الميزات الجديدة
echo "<div class='alert alert-primary mt-4'>
<h5><i class='fas fa-star me-2'></i>الميزات الجديدة المتاحة:</h5>
<ul class='mb-0'>
<li><strong>عرض تفاصيل المعلم:</strong> صفحة شاملة تعرض جميع بيانات المعلم مع الإحصائيات</li>
<li><strong>تعديل بيانات المعلم:</strong> إمكانية تحديث جميع البيانات الشخصية والمهنية</li>
<li><strong>حذف المعلم:</strong> حذف آمن مع حذف جميع البيانات المرتبطة</li>
<li><strong>إحصائيات شاملة:</strong> عرض إحصائيات كل معلم (تحضيرات، أنشطة، ملفات، إلخ)</li>
<li><strong>إدارة الحسابات:</strong> إنشاء وإدارة حسابات المستخدمين للمعلمين</li>
</ul>
</div>";

// روابط سريعة للاختبار
echo "<div class='alert alert-warning mt-4'>
<h5><i class='fas fa-link me-2'></i>روابط سريعة للاختبار:</h5>
<div class='row'>
<div class='col-md-6'>
<ul class='list-unstyled'>
<li><a href='teachers/list.php' class='btn btn-sm btn-outline-primary me-2 mb-2'><i class='fas fa-users me-1'></i>قائمة المعلمين</a></li>
<li><a href='teachers/add.php' class='btn btn-sm btn-outline-success me-2 mb-2'><i class='fas fa-user-plus me-1'></i>إضافة معلم جديد</a></li>
</ul>
</div>
<div class='col-md-6'>
<ul class='list-unstyled'>
<li><a href='login.php' class='btn btn-sm btn-outline-info me-2 mb-2'><i class='fas fa-sign-in-alt me-1'></i>تسجيل الدخول</a></li>
<li><a href='index.php' class='btn btn-sm btn-outline-secondary me-2 mb-2'><i class='fas fa-home me-1'></i>الصفحة الرئيسية</a></li>
</ul>
</div>
</div>
</div>";

echo "</div>
</div>
</div>
</div>
</div>

<script>
function confirmDelete(teacherId, teacherName) {
    if (confirm('هل أنت متأكد من حذف المعلم \"' + teacherName + '\"؟\\n\\nسيتم حذف جميع البيانات المرتبطة بهذا المعلم.')) {
        window.location.href = 'teachers/delete.php?id=' + teacherId;
    }
}
</script>

</body>
</html>";
?>
