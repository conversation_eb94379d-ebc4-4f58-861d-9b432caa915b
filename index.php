<?php
require_once 'config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('login.php');
}

// الحصول على بيانات المستخدم الحالي
$currentUser = null;
$currentTeacher = null;

try {
    $stmt = $db->query("SELECT * FROM users WHERE id = ?", [$_SESSION['user_id']]);
    $currentUser = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($currentUser['role'] === 'teacher') {
        $stmt = $db->query("SELECT * FROM teachers WHERE user_id = ?", [$_SESSION['user_id']]);
        $currentTeacher = $stmt->fetch(PDO::FETCH_ASSOC);
    }
} catch (Exception $e) {
    showMessage('خطأ في جلب بيانات المستخدم', 'error');
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - الصفحة الرئيسية</title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-school me-2"></i>
                <?php echo APP_NAME; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    
                    <?php if (isAdmin()): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-users me-1"></i>
                            إدارة المعلمين
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="teachers/list.php">قائمة المعلمين</a></li>
                            <li><a class="dropdown-item" href="teachers/add.php">إضافة معلم</a></li>
                            <li><a class="dropdown-item" href="attendance/list.php">الحضور والغياب</a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-bar me-1"></i>
                            التقارير
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="reports/general.php">التقارير العامة</a></li>
                            <li><a class="dropdown-item" href="reports/curriculum.php">متابعة المنهج</a></li>
                            <li><a class="dropdown-item" href="reports/activities.php">الأنشطة والمسابقات</a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link" href="warnings/list.php">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            التنويهات
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <?php if (isTeacher()): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="preparations/my.php">
                            <i class="fas fa-book me-1"></i>
                            التحضير اليومي
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link" href="activities/my.php">
                            <i class="fas fa-trophy me-1"></i>
                            أنشطتي
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link" href="curriculum/my.php">
                            <i class="fas fa-tasks me-1"></i>
                            متابعة المنهج
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo $currentUser['username']; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <?php displayMessage(); ?>
        
        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-gradient-primary text-white">
                    <div class="card-body">
                        <h2 class="card-title">
                            <i class="fas fa-hand-wave me-2"></i>
                            مرحباً، <?php echo $currentUser['username']; ?>
                        </h2>
                        <p class="card-text">
                            <?php if (isAdmin()): ?>
                                مرحباً بك في لوحة تحكم المدير. يمكنك من هنا إدارة جميع المعلمين ومتابعة أعمالهم.
                            <?php else: ?>
                                مرحباً بك في نظام الأرشفة. يمكنك من هنا إدارة أعمالك اليومية ومتابعة تقدمك.
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Cards -->
        <div class="row">
            <?php if (isAdmin()): ?>
            <!-- Admin Dashboard -->
            <div class="col-md-3 mb-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-users fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">المعلمون</h5>
                        <?php
                        $stmt = $db->query("SELECT COUNT(*) as count FROM teachers");
                        $teacherCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                        ?>
                        <h3 class="text-primary"><?php echo $teacherCount; ?></h3>
                        <a href="teachers/list.php" class="btn btn-primary">عرض الكل</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-user-check fa-3x text-success mb-3"></i>
                        <h5 class="card-title">الحاضرون اليوم</h5>
                        <?php
                        $stmt = $db->query("SELECT COUNT(*) as count FROM teachers WHERE is_present = 1");
                        $presentCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                        ?>
                        <h3 class="text-success"><?php echo $presentCount; ?></h3>
                        <a href="attendance/today.php" class="btn btn-success">التفاصيل</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-trophy fa-3x text-warning mb-3"></i>
                        <h5 class="card-title">الأنشطة النشطة</h5>
                        <?php
                        $stmt = $db->query("SELECT COUNT(*) as count FROM activities WHERE status = 'ongoing'");
                        $activeActivities = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                        ?>
                        <h3 class="text-warning"><?php echo $activeActivities; ?></h3>
                        <a href="activities/list.php" class="btn btn-warning">عرض الكل</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                        <h5 class="card-title">التنويهات الجديدة</h5>
                        <?php
                        $stmt = $db->query("SELECT COUNT(*) as count FROM warnings WHERE is_read = 0");
                        $unreadWarnings = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                        ?>
                        <h3 class="text-danger"><?php echo $unreadWarnings; ?></h3>
                        <a href="warnings/list.php" class="btn btn-danger">عرض الكل</a>
                    </div>
                </div>
            </div>
            
            <?php else: ?>
            <!-- Teacher Dashboard -->
            <div class="col-md-4 mb-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-book fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">التحضير اليومي</h5>
                        <?php
                        $stmt = $db->query("SELECT COUNT(*) as count FROM daily_preparations WHERE teacher_id = ? AND preparation_date = CURRENT_DATE", [$currentTeacher['id']]);
                        $todayPrep = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                        ?>
                        <p class="text-muted">
                            <?php echo $todayPrep > 0 ? 'تم التحضير لليوم' : 'لم يتم التحضير بعد'; ?>
                        </p>
                        <div class="d-grid gap-2">
                            <a href="preparations/my.php" class="btn btn-primary">عرض تحضيراتي</a>
                            <a href="preparations/add.php" class="btn btn-outline-primary">
                                <i class="fas fa-plus me-2"></i>
                                إضافة تحضير جديد
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-trophy fa-3x text-success mb-3"></i>
                        <h5 class="card-title">أنشطتي</h5>
                        <?php
                        $stmt = $db->query("SELECT COUNT(*) as count FROM activities WHERE teacher_id = ?", [$currentTeacher['id']]);
                        $myActivities = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                        ?>
                        <h3 class="text-success"><?php echo $myActivities; ?></h3>
                        <div class="d-grid gap-2">
                            <a href="activities/my.php" class="btn btn-success">عرض الكل</a>
                            <a href="activities/add.php" class="btn btn-outline-success">
                                <i class="fas fa-plus me-2"></i>
                                إضافة نشاط جديد
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-exclamation-circle fa-3x text-warning mb-3"></i>
                        <h5 class="card-title">التنويهات</h5>
                        <?php
                        $stmt = $db->query("SELECT COUNT(*) as count FROM warnings WHERE teacher_id = ? AND is_read = 0", [$currentTeacher['id']]);
                        $myWarnings = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                        ?>
                        <h3 class="text-warning"><?php echo $myWarnings; ?></h3>
                        <a href="warnings/my.php" class="btn btn-warning">عرض الكل</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- صف ثاني للإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-4 mb-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-tasks fa-3x text-info mb-3"></i>
                        <h5 class="card-title">متابعة المنهج</h5>
                        <?php
                        $stmt = $db->query("SELECT COUNT(*) as count FROM curriculum_progress WHERE teacher_id = ?", [$currentTeacher['id']]);
                        $myCurriculum = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                        ?>
                        <h3 class="text-info"><?php echo $myCurriculum; ?></h3>
                        <div class="d-grid gap-2">
                            <a href="curriculum/my.php" class="btn btn-info">عرض التقدم</a>
                            <a href="curriculum/add.php" class="btn btn-outline-info">
                                <i class="fas fa-plus me-2"></i>
                                إضافة وحدة جديدة
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-folder fa-3x text-secondary mb-3"></i>
                        <h5 class="card-title">ملفاتي</h5>
                        <?php
                        $stmt = $db->query("SELECT COUNT(*) as count FROM files WHERE teacher_id = ?", [$currentTeacher['id']]);
                        $myFiles = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                        ?>
                        <h3 class="text-secondary"><?php echo $myFiles; ?></h3>
                        <div class="d-grid gap-2">
                            <a href="files/my.php" class="btn btn-secondary">عرض الملفات</a>
                            <a href="files/my.php#upload" class="btn btn-outline-secondary">
                                <i class="fas fa-upload me-2"></i>
                                رفع ملف جديد
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-user-circle fa-3x text-dark mb-3"></i>
                        <h5 class="card-title">ملفي الشخصي</h5>
                        <p class="text-muted">إدارة البيانات الشخصية</p>
                        <div class="d-grid gap-2">
                            <a href="profile.php" class="btn btn-dark">عرض الملف</a>
                            <a href="profile.php#edit" class="btn btn-outline-dark">
                                <i class="fas fa-edit me-2"></i>
                                تحديث البيانات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Recent Activities -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clock me-2"></i>
                            الأنشطة الأخيرة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>النشاط</th>
                                        <th>المعلم</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $sql = "SELECT a.*, t.first_name, t.last_name 
                                           FROM activities a 
                                           JOIN teachers t ON a.teacher_id = t.id ";
                                    
                                    if (isTeacher()) {
                                        $sql .= "WHERE a.teacher_id = ? ";
                                        $params = [$currentTeacher['id']];
                                    } else {
                                        $params = [];
                                    }
                                    
                                    $sql .= "ORDER BY a.created_at DESC LIMIT 5";
                                    
                                    $stmt = $db->query($sql, $params);
                                    $activities = $stmt->fetchAll(PDO::FETCH_ASSOC);
                                    
                                    if (empty($activities)): ?>
                                        <tr>
                                            <td colspan="4" class="text-center text-muted">لا توجد أنشطة حديثة</td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($activities as $activity): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($activity['title']); ?></td>
                                            <td><?php echo htmlspecialchars($activity['first_name'] . ' ' . $activity['last_name']); ?></td>
                                            <td><?php echo formatDateArabic($activity['created_at']); ?></td>
                                            <td>
                                                <?php
                                                $statusClass = '';
                                                $statusText = '';
                                                switch ($activity['status']) {
                                                    case 'planned':
                                                        $statusClass = 'badge bg-secondary';
                                                        $statusText = 'مخطط';
                                                        break;
                                                    case 'ongoing':
                                                        $statusClass = 'badge bg-primary';
                                                        $statusText = 'جاري';
                                                        break;
                                                    case 'completed':
                                                        $statusClass = 'badge bg-success';
                                                        $statusText = 'مكتمل';
                                                        break;
                                                    case 'cancelled':
                                                        $statusClass = 'badge bg-danger';
                                                        $statusText = 'ملغي';
                                                        break;
                                                }
                                                ?>
                                                <span class="<?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
</body>
</html>
