<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار مباشر لمشكلة تسجيل الدخول</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap' rel='stylesheet'>
    <style>body { font-family: 'Cairo', sans-serif; }</style>
</head>
<body class='bg-light'>
<div class='container mt-5'>
<div class='row justify-content-center'>
<div class='col-md-12'>
<div class='card'>
<div class='card-header bg-warning text-dark'>
<h4 class='mb-0'><i class='fas fa-microscope me-2'></i>اختبار مباشر لمشكلة تسجيل الدخول</h4>
</div>
<div class='card-body'>";

// إنشاء معلم جديد مع تتبع مفصل
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_teacher'])) {
    $inputUsername = $_POST['input_username'] ?? '';
    $inputPassword = $_POST['input_password'] ?? '';
    
    echo "<h5 class='text-primary'><i class='fas fa-user-plus me-2'></i>خطوة 1: إنشاء معلم جديد</h5>";
    
    echo "<div class='alert alert-info'>
    <strong>البيانات المدخلة في النموذج:</strong><br>
    اسم المستخدم الأصلي: '<span style='background: yellow; padding: 2px;'>$inputUsername</span>' (طول: " . strlen($inputUsername) . ")<br>
    كلمة المرور الأصلية: '<span style='background: yellow; padding: 2px;'>$inputPassword</span>' (طول: " . strlen($inputPassword) . ")
    </div>";
    
    // محاكاة نفس المعالجة في teachers/add.php
    $processedUsername = trim($inputUsername);
    $processedPassword = $inputPassword;
    
    echo "<div class='alert alert-secondary'>
    <strong>بعد المعالجة (trim):</strong><br>
    اسم المستخدم المعالج: '<span style='background: lightblue; padding: 2px;'>$processedUsername</span>' (طول: " . strlen($processedUsername) . ")<br>
    كلمة المرور: '<span style='background: lightblue; padding: 2px;'>$processedPassword</span>' (طول: " . strlen($processedPassword) . ")
    </div>";
    
    // فحص وجود اسم المستخدم
    try {
        $stmt = $db->query("SELECT * FROM users WHERE username = ?", [$processedUsername]);
        $existingUser = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($existingUser) {
            echo "<div class='alert alert-warning'>
            <h6>اسم المستخدم موجود بالفعل!</h6>
            <p>سيتم تحديث كلمة المرور للمستخدم الموجود...</p>
            </div>";
            
            // تحديث كلمة المرور
            $hashedPassword = password_hash($processedPassword, PASSWORD_DEFAULT);
            $stmt = $db->query("UPDATE users SET password = ?, is_active = 1 WHERE username = ?", 
                              [$hashedPassword, $processedUsername]);
            
            echo "<div class='alert alert-success'>تم تحديث كلمة المرور للمستخدم الموجود</div>";
        } else {
            // إنشاء مستخدم جديد
            echo "<div class='alert alert-info'>إنشاء مستخدم جديد...</div>";
            
            $db->getConnection()->beginTransaction();
            
            $hashedPassword = password_hash($processedPassword, PASSWORD_DEFAULT);
            
            echo "<div class='alert alert-secondary'>
            <strong>تشفير كلمة المرور:</strong><br>
            كلمة المرور الأصلية: '$processedPassword'<br>
            كلمة المرور المشفرة: " . substr($hashedPassword, 0, 60) . "...
            </div>";
            
            $stmt = $db->query("INSERT INTO users (username, password, role, is_active, created_at) VALUES (?, ?, 'teacher', 1, datetime('now'))", 
                              [$processedUsername, $hashedPassword]);
            $userId = $db->lastInsertId();
            
            echo "<div class='alert alert-success'>تم إنشاء المستخدم - ID: $userId</div>";
            
            // إنشاء ملف المعلم
            $employeeId = 'LIVE' . time();
            $stmt = $db->query("INSERT INTO teachers (user_id, employee_id, first_name, last_name, email, subject, grade_level, hire_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", 
                              [$userId, $employeeId, 'معلم', 'مباشر', '<EMAIL>', 'اختبار مباشر', 'الصف الأول', date('Y-m-d')]);
            
            echo "<div class='alert alert-success'>تم إنشاء ملف المعلم - رقم الموظف: $employeeId</div>";
            
            $db->getConnection()->commit();
        }
        
        // فحص فوري للمستخدم المحفوظ
        echo "<h6 class='text-info mt-3'>فحص فوري للمستخدم المحفوظ:</h6>";
        
        $stmt = $db->query("SELECT * FROM users WHERE username = ?", [$processedUsername]);
        $savedUser = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($savedUser) {
            echo "<div class='alert alert-success'>
            <h6>المستخدم محفوظ في قاعدة البيانات:</h6>
            <table class='table table-sm table-bordered'>
            <tr><td><strong>ID:</strong></td><td>{$savedUser['id']}</td></tr>
            <tr><td><strong>اسم المستخدم المحفوظ:</strong></td><td><code>{$savedUser['username']}</code></td></tr>
            <tr><td><strong>طول اسم المستخدم:</strong></td><td>" . strlen($savedUser['username']) . " حرف</td></tr>
            <tr><td><strong>الدور:</strong></td><td>{$savedUser['role']}</td></tr>
            <tr><td><strong>نشط:</strong></td><td>" . ($savedUser['is_active'] ? 'نعم' : 'لا') . "</td></tr>
            <tr><td><strong>تاريخ الإنشاء:</strong></td><td>{$savedUser['created_at']}</td></tr>
            </table>
            </div>";
            
            // اختبار كلمة المرور فوراً
            echo "<h6 class='text-success mt-3'>اختبار كلمة المرور فوراً:</h6>";
            
            if (password_verify($processedPassword, $savedUser['password'])) {
                echo "<div class='alert alert-success'>
                <i class='fas fa-check me-2'></i>كلمة المرور صحيحة!
                </div>";
                
                // حفظ البيانات للاختبار التالي
                $_SESSION['test_username'] = $processedUsername;
                $_SESSION['test_password'] = $processedPassword;
                
                echo "<div class='alert alert-primary'>
                <h6>البيانات جاهزة للاختبار:</h6>
                <p><strong>اسم المستخدم:</strong> <code>$processedUsername</code></p>
                <p><strong>كلمة المرور:</strong> <code>$processedPassword</code></p>
                <p>استخدم هذه البيانات في القسم التالي لاختبار تسجيل الدخول.</p>
                </div>";
                
            } else {
                echo "<div class='alert alert-danger'>
                <i class='fas fa-times me-2'></i>كلمة المرور خاطئة!
                </div>";
            }
        } else {
            echo "<div class='alert alert-danger'>
            <i class='fas fa-times me-2'></i>لم يتم العثور على المستخدم المحفوظ!
            </div>";
        }
        
    } catch (Exception $e) {
        if (isset($db)) {
            $db->getConnection()->rollBack();
        }
        echo "<div class='alert alert-danger'>
        <i class='fas fa-times me-2'></i>خطأ في إنشاء المعلم: " . $e->getMessage() . "
        </div>";
    }
}

// اختبار تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_login'])) {
    $loginUsername = $_POST['login_username'] ?? '';
    $loginPassword = $_POST['login_password'] ?? '';
    
    echo "<h5 class='text-success'><i class='fas fa-sign-in-alt me-2'></i>خطوة 2: اختبار تسجيل الدخول</h5>";
    
    echo "<div class='alert alert-info'>
    <strong>البيانات المدخلة لتسجيل الدخول:</strong><br>
    اسم المستخدم: '<span style='background: yellow; padding: 2px;'>$loginUsername</span>' (طول: " . strlen($loginUsername) . ")<br>
    كلمة المرور: '<span style='background: yellow; padding: 2px;'>$loginPassword</span>' (طول: " . strlen($loginPassword) . ")
    </div>";
    
    // محاكاة نفس المعالجة في login.php
    $processedLoginUsername = trim($loginUsername);
    $processedLoginPassword = $loginPassword;
    
    echo "<div class='alert alert-secondary'>
    <strong>بعد المعالجة (trim):</strong><br>
    اسم المستخدم المعالج: '<span style='background: lightgreen; padding: 2px;'>$processedLoginUsername</span>' (طول: " . strlen($processedLoginUsername) . ")<br>
    كلمة المرور: '<span style='background: lightgreen; padding: 2px;'>$processedLoginPassword</span>' (طول: " . strlen($processedLoginPassword) . ")
    </div>";
    
    try {
        // محاكاة نفس الاستعلام في login.php
        $stmt = $db->query("SELECT * FROM users WHERE username = ? AND is_active = 1", [$processedLoginUsername]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            echo "<div class='alert alert-success'>
            <h6>تم العثور على المستخدم النشط:</h6>
            <table class='table table-sm table-bordered'>
            <tr><td><strong>ID:</strong></td><td>{$user['id']}</td></tr>
            <tr><td><strong>اسم المستخدم في قاعدة البيانات:</strong></td><td><code>{$user['username']}</code></td></tr>
            <tr><td><strong>طول اسم المستخدم:</strong></td><td>" . strlen($user['username']) . " حرف</td></tr>
            <tr><td><strong>الدور:</strong></td><td>{$user['role']}</td></tr>
            <tr><td><strong>نشط:</strong></td><td>" . ($user['is_active'] ? 'نعم' : 'لا') . "</td></tr>
            </table>
            </div>";
            
            // مقارنة أسماء المستخدمين
            if ($processedLoginUsername === $user['username']) {
                echo "<div class='alert alert-success'>
                <i class='fas fa-check me-2'></i>أسماء المستخدمين متطابقة تماماً!
                </div>";
            } else {
                echo "<div class='alert alert-danger'>
                <i class='fas fa-times me-2'></i>أسماء المستخدمين غير متطابقة!<br>
                المدخل: '$processedLoginUsername'<br>
                المحفوظ: '{$user['username']}'
                </div>";
            }
            
            // اختبار كلمة المرور
            if (password_verify($processedLoginPassword, $user['password'])) {
                echo "<div class='alert alert-success'>
                <h6><i class='fas fa-check me-2'></i>تسجيل الدخول نجح!</h6>
                <p>جميع الشروط مستوفاة:</p>
                <ul>
                <li>✅ المستخدم موجود</li>
                <li>✅ الحساب نشط</li>
                <li>✅ اسم المستخدم صحيح</li>
                <li>✅ كلمة المرور صحيحة</li>
                </ul>
                <div class='mt-3'>
                <a href='login.php' class='btn btn-success'>جرب تسجيل الدخول الحقيقي الآن</a>
                </div>
                </div>";
            } else {
                echo "<div class='alert alert-danger'>
                <i class='fas fa-times me-2'></i>كلمة المرور خاطئة!
                </div>";
                
                // اختبار كلمات مرور مختلفة
                $testPasswords = [$loginPassword, trim($loginPassword), 'teacher123', 'test123'];
                echo "<div class='alert alert-warning'>
                <h6>اختبار كلمات مرور مختلفة:</h6>";
                
                foreach ($testPasswords as $testPass) {
                    if (password_verify($testPass, $user['password'])) {
                        echo "<div class='alert alert-success'>✅ كلمة المرور الصحيحة: '$testPass'</div>";
                        break;
                    } else {
                        echo "<div class='alert alert-secondary'>❌ ليست: '$testPass'</div>";
                    }
                }
                echo "</div>";
            }
        } else {
            echo "<div class='alert alert-danger'>
            <i class='fas fa-times me-2'></i>لم يتم العثور على المستخدم أو الحساب غير نشط!
            </div>";
            
            // البحث بدون شرط is_active
            $stmt = $db->query("SELECT * FROM users WHERE username = ?", [$processedLoginUsername]);
            $userAny = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($userAny) {
                echo "<div class='alert alert-warning'>
                المستخدم موجود لكنه غير نشط! (is_active = {$userAny['is_active']})
                </div>";
            } else {
                echo "<div class='alert alert-danger'>
                المستخدم غير موجود نهائياً في قاعدة البيانات!
                </div>";
                
                // عرض أسماء المستخدمين المشابهة
                $stmt = $db->query("SELECT username FROM users WHERE username LIKE ? LIMIT 5", ['%' . $processedLoginUsername . '%']);
                $similar = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                if (!empty($similar)) {
                    echo "<div class='alert alert-info'>
                    <h6>أسماء مستخدمين مشابهة:</h6>";
                    foreach ($similar as $sim) {
                        echo "- '$sim'<br>";
                    }
                    echo "</div>";
                }
            }
        }
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>
        <i class='fas fa-times me-2'></i>خطأ في اختبار تسجيل الدخول: " . $e->getMessage() . "
        </div>";
    }
}

// نماذج الاختبار
echo "<div class='row'>
<div class='col-md-6'>
<div class='card border-primary'>
<div class='card-header bg-primary text-white'>
<h6 class='mb-0'><i class='fas fa-user-plus me-2'></i>خطوة 1: إنشاء معلم جديد</h6>
</div>
<div class='card-body'>
<form method='POST'>
<div class='mb-3'>
<label for='input_username' class='form-label'>اسم المستخدم:</label>
<input type='text' class='form-control' id='input_username' name='input_username' 
       value='livetest" . time() . "' required>
<small class='text-muted'>سيتم إنشاء معلم بهذا الاسم</small>
</div>
<div class='mb-3'>
<label for='input_password' class='form-label'>كلمة المرور:</label>
<input type='text' class='form-control' id='input_password' name='input_password' 
       value='livepass123' required>
<small class='text-muted'>احفظ هذه كلمة المرور</small>
</div>
<button type='submit' name='create_teacher' class='btn btn-primary w-100'>
<i class='fas fa-plus me-2'></i>إنشاء المعلم
</button>
</form>
</div>
</div>
</div>

<div class='col-md-6'>
<div class='card border-success'>
<div class='card-header bg-success text-white'>
<h6 class='mb-0'><i class='fas fa-sign-in-alt me-2'></i>خطوة 2: اختبار تسجيل الدخول</h6>
</div>
<div class='card-body'>
<form method='POST'>
<div class='mb-3'>
<label for='login_username' class='form-label'>اسم المستخدم:</label>
<input type='text' class='form-control' id='login_username' name='login_username' 
       value='" . ($_SESSION['test_username'] ?? '') . "' required>
<small class='text-muted'>انسخ من الخطوة الأولى</small>
</div>
<div class='mb-3'>
<label for='login_password' class='form-label'>كلمة المرور:</label>
<input type='text' class='form-control' id='login_password' name='login_password' 
       value='" . ($_SESSION['test_password'] ?? '') . "' required>
<small class='text-muted'>انسخ من الخطوة الأولى</small>
</div>
<button type='submit' name='test_login' class='btn btn-success w-100'>
<i class='fas fa-sign-in-alt me-2'></i>اختبار تسجيل الدخول
</button>
</form>
</div>
</div>
</div>
</div>";

// عرض آخر المستخدمين
echo "<h5 class='text-primary mt-4'><i class='fas fa-database me-2'></i>آخر المستخدمين في قاعدة البيانات:</h5>";

try {
    $stmt = $db->query("SELECT u.*, t.first_name, t.last_name 
                       FROM users u 
                       LEFT JOIN teachers t ON u.id = t.user_id 
                       WHERE u.role = 'teacher' 
                       ORDER BY u.created_at DESC 
                       LIMIT 5");
    $recentUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($recentUsers)) {
        echo "<div class='table-responsive'>
        <table class='table table-striped table-sm'>
        <thead class='table-dark'>
        <tr>
        <th>ID</th>
        <th>اسم المستخدم</th>
        <th>اسم المعلم</th>
        <th>نشط</th>
        <th>تاريخ الإنشاء</th>
        <th>اختبار سريع</th>
        </tr>
        </thead>
        <tbody>";
        
        foreach ($recentUsers as $user) {
            $teacherName = $user['first_name'] ? $user['first_name'] . ' ' . $user['last_name'] : 'غير محدد';
            $isActive = $user['is_active'] ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-danger">غير نشط</span>';
            $createdAt = date('Y-m-d H:i', strtotime($user['created_at']));
            
            echo "<tr>
            <td>{$user['id']}</td>
            <td><code>{$user['username']}</code></td>
            <td>" . htmlspecialchars($teacherName) . "</td>
            <td>$isActive</td>
            <td>$createdAt</td>
            <td>
                <button class='btn btn-sm btn-outline-primary' onclick='copyToLogin(\"{$user['username']}\")'>
                    نسخ للاختبار
                </button>
            </td>
            </tr>";
        }
        
        echo "</tbody></table></div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>خطأ في جلب المستخدمين: " . $e->getMessage() . "</div>";
}

echo "</div>
</div>
</div>
</div>
</div>

<script>
function copyToLogin(username) {
    document.getElementById('login_username').value = username;
    const password = prompt('أدخل كلمة المرور لهذا المستخدم:');
    if (password) {
        document.getElementById('login_password').value = password;
    }
}
</script>

</body>
</html>";
?>
