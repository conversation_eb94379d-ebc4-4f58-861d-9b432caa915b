<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار إصلاح حذف المعلمين</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap' rel='stylesheet'>
    <style>body { font-family: 'Cairo', sans-serif; }</style>
</head>
<body class='bg-light'>
<div class='container mt-5'>
<div class='row justify-content-center'>
<div class='col-md-10'>
<div class='card'>
<div class='card-header bg-primary text-white'>
<h4 class='mb-0'><i class='fas fa-test-tube me-2'></i>اختبار إصلاح حذف المعلمين</h4>
</div>
<div class='card-body'>";

echo "<div class='alert alert-success'>
<h5><i class='fas fa-check-circle me-2'></i>تم إصلاح مشكلة حذف المعلمين!</h5>
<p class='mb-0'>تم تبسيط عملية الحذف وإزالة الاعتماد على SweetAlert2.</p>
</div>";

// فحص الإصلاحات
echo "<h5 class='text-primary'><i class='fas fa-wrench me-2'></i>الإصلاحات المطبقة:</h5>";

$fixes = [
    'تغيير زر الحذف من button إلى رابط مباشر',
    'استخدام confirm() البسيط بدلاً من SweetAlert2',
    'إزالة الكود JavaScript المعقد',
    'تبسيط عملية التأكيد',
    'إنشاء صفحة حذف مبسطة للاختبار'
];

foreach ($fixes as $fix) {
    echo "<div class='alert alert-success alert-sm'><i class='fas fa-check me-2'></i>$fix</div>";
}

// عرض قائمة المعلمين للاختبار
echo "<h5 class='text-primary mt-4'><i class='fas fa-users me-2'></i>اختبار الحذف - قائمة المعلمين:</h5>";

try {
    $stmt = $db->query("SELECT t.*, u.username 
                       FROM teachers t 
                       LEFT JOIN users u ON t.user_id = u.id 
                       ORDER BY t.first_name, t.last_name 
                       LIMIT 10");
    $teachers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($teachers)) {
        echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle me-2'></i>لا يوجد معلمون في النظام</div>";
        
        // إنشاء معلم تجريبي للاختبار
        echo "<div class='alert alert-info'>
        <h6>إنشاء معلم تجريبي للاختبار:</h6>
        <form method='POST'>
        <button type='submit' name='create_test_teacher' class='btn btn-primary'>إنشاء معلم تجريبي</button>
        </form>
        </div>";
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_test_teacher'])) {
            try {
                $testUsername = 'test_teacher_' . time();
                $testPassword = password_hash('test123', PASSWORD_DEFAULT);
                
                // إضافة مستخدم تجريبي
                $stmt = $db->query("INSERT INTO users (username, password, role, is_active) VALUES (?, ?, 'teacher', 1)", 
                                  [$testUsername, $testPassword]);
                $testUserId = $db->lastInsertId();
                
                // إضافة معلم تجريبي
                $stmt = $db->query("INSERT INTO teachers (user_id, employee_id, first_name, last_name, subject, grade_level, hire_date, email) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", 
                                  [$testUserId, 'TEST' . time(), 'معلم', 'تجريبي', 'اختبار الحذف', 'الصف الأول', date('Y-m-d'), '<EMAIL>']);
                
                echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>تم إنشاء معلم تجريبي بنجاح! <a href='?' class='btn btn-sm btn-primary ms-2'>تحديث الصفحة</a></div>";
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ في إنشاء المعلم التجريبي: " . $e->getMessage() . "</div>";
            }
        }
        
    } else {
        echo "<div class='table-responsive'>
        <table class='table table-striped'>
        <thead class='table-dark'>
        <tr>
        <th>ID</th>
        <th>الاسم</th>
        <th>رقم الموظف</th>
        <th>المادة</th>
        <th>حساب المستخدم</th>
        <th>الإجراءات</th>
        </tr>
        </thead>
        <tbody>";
        
        foreach ($teachers as $teacher) {
            $teacherName = $teacher['first_name'] . ' ' . $teacher['last_name'];
            $userAccount = $teacher['username'] ? $teacher['username'] : 'لا يوجد';
            
            echo "<tr>
            <td>{$teacher['id']}</td>
            <td><strong>" . htmlspecialchars($teacherName) . "</strong></td>
            <td>" . htmlspecialchars($teacher['employee_id']) . "</td>
            <td>" . htmlspecialchars($teacher['subject']) . "</td>
            <td>$userAccount</td>
            <td>
                <a href='teachers/view.php?id={$teacher['id']}' class='btn btn-sm btn-outline-primary me-1' title='عرض'>
                    <i class='fas fa-eye'></i>
                </a>
                <a href='teachers/edit.php?id={$teacher['id']}' class='btn btn-sm btn-outline-warning me-1' title='تعديل'>
                    <i class='fas fa-edit'></i>
                </a>
                <a href='teachers/delete.php?id={$teacher['id']}' 
                   class='btn btn-sm btn-outline-danger me-1' 
                   onclick='return confirm(\"هل أنت متأكد من حذف المعلم " . htmlspecialchars($teacherName) . "؟\")' 
                   title='حذف (الطريقة الأصلية)'>
                    <i class='fas fa-trash'></i>
                </a>
                <a href='teachers/delete_simple.php?id={$teacher['id']}' 
                   class='btn btn-sm btn-danger' 
                   title='حذف (الطريقة المبسطة)'>
                    <i class='fas fa-trash-alt'></i> اختبار
                </a>
            </td>
            </tr>";
        }
        
        echo "</tbody></table></div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ في جلب قائمة المعلمين: " . $e->getMessage() . "</div>";
}

// تعليمات الاختبار
echo "<h5 class='text-primary mt-4'><i class='fas fa-clipboard-list me-2'></i>تعليمات الاختبار:</h5>";

echo "<div class='alert alert-info'>
<ol class='mb-0'>
<li><strong>الطريقة الأصلية:</strong> اضغط على زر الحذف الأحمر العادي (سيطلب تأكيد بسيط)</li>
<li><strong>الطريقة المبسطة:</strong> اضغط على زر \"اختبار\" (سيأخذك لصفحة تأكيد مفصلة)</li>
<li><strong>التأكد من الحذف:</strong> تحقق من أن المعلم اختفى من القائمة بعد الحذف</li>
<li><strong>فحص البيانات المرتبطة:</strong> تأكد من حذف جميع البيانات المرتبطة</li>
</ol>
</div>";

// معلومات إضافية
echo "<div class='alert alert-warning mt-4'>
<h5><i class='fas fa-exclamation-triangle me-2'></i>ملاحظات مهمة:</h5>
<ul class='mb-0'>
<li>تم تبسيط عملية الحذف لتجنب مشاكل JavaScript</li>
<li>يتم الآن استخدام confirm() البسيط بدلاً من SweetAlert2</li>
<li>تم إنشاء صفحة حذف مبسطة للاختبار والتشخيص</li>
<li>جميع البيانات المرتبطة بالمعلم سيتم حذفها تلقائياً</li>
</ul>
</div>";

// روابط سريعة
echo "<div class='alert alert-primary mt-4'>
<h5><i class='fas fa-link me-2'></i>روابط سريعة:</h5>
<div class='row'>
<div class='col-md-6'>
<ul class='list-unstyled'>
<li><a href='teachers/list.php' class='btn btn-sm btn-outline-primary me-2 mb-2'><i class='fas fa-users me-1'></i>قائمة المعلمين</a></li>
<li><a href='teachers/add.php' class='btn btn-sm btn-outline-success me-2 mb-2'><i class='fas fa-user-plus me-1'></i>إضافة معلم جديد</a></li>
</ul>
</div>
<div class='col-md-6'>
<ul class='list-unstyled'>
<li><a href='debug_delete_teacher.php' class='btn btn-sm btn-outline-warning me-2 mb-2'><i class='fas fa-bug me-1'></i>تشخيص الحذف</a></li>
<li><a href='index.php' class='btn btn-sm btn-outline-secondary me-2 mb-2'><i class='fas fa-home me-1'></i>الصفحة الرئيسية</a></li>
</ul>
</div>
</div>
</div>";

echo "</div>
</div>
</div>
</div>
</div>
</body>
</html>";
?>
