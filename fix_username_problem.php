<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إصلاح مشكلة اسم المستخدم</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap' rel='stylesheet'>
    <style>body { font-family: 'Cairo', sans-serif; }</style>
</head>
<body class='bg-light'>
<div class='container mt-5'>
<div class='row justify-content-center'>
<div class='col-md-10'>
<div class='card'>
<div class='card-header bg-success text-white'>
<h4 class='mb-0'><i class='fas fa-wrench me-2'></i>إصلاح مشكلة اسم المستخدم</h4>
</div>
<div class='card-body'>";

echo "<div class='alert alert-success'>
<h5><i class='fas fa-check-circle me-2'></i>تم اكتشاف وإصلاح المشكلة!</h5>
<p class='mb-0'>المشكلة كانت في دالة sanitize التي تؤثر على اسم المستخدم. تم إصلاحها.</p>
</div>";

// شرح المشكلة
echo "<h5 class='text-primary'><i class='fas fa-bug me-2'></i>المشكلة المكتشفة:</h5>";

echo "<div class='alert alert-warning'>
<h6>دالة sanitize تؤثر على اسم المستخدم:</h6>
<p>كانت دالة <code>sanitize()</code> تستخدم على اسم المستخدم، وهذه الدالة تحتوي على:</p>
<ul class='mb-0'>
<li><code>htmlspecialchars()</code> - تحويل الرموز الخاصة</li>
<li><code>strip_tags()</code> - إزالة HTML tags</li>
<li><code>trim()</code> - إزالة المسافات</li>
</ul>
<p class='mt-2'>هذا قد يؤثر على اسم المستخدم إذا كان يحتوي على رموز خاصة.</p>
</div>";

// الإصلاح المطبق
echo "<h5 class='text-primary'><i class='fas fa-tools me-2'></i>الإصلاح المطبق:</h5>";

echo "<div class='alert alert-success'>
<h6>تم تغيير معالجة اسم المستخدم:</h6>
<div class='row'>
<div class='col-md-6'>
<strong>قبل الإصلاح:</strong>
<pre><code>\$username = sanitize(\$_POST['username'] ?? '');</code></pre>
</div>
<div class='col-md-6'>
<strong>بعد الإصلاح:</strong>
<pre><code>\$username = trim(\$_POST['username'] ?? '');</code></pre>
</div>
</div>
<p class='mb-0'>الآن يتم استخدام <code>trim()</code> فقط لإزالة المسافات الزائدة.</p>
</div>";

// اختبار شامل
echo "<h5 class='text-primary'><i class='fas fa-test-tube me-2'></i>اختبار شامل للإصلاح:</h5>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['comprehensive_test'])) {
    $testCases = [
        ['username' => 'normaluser', 'password' => 'pass123'],
        ['username' => 'user.with.dots', 'password' => 'pass123'],
        ['username' => 'user_with_underscores', 'password' => 'pass123'],
        ['username' => 'user-with-dashes', 'password' => 'pass123'],
        ['username' => 'user123', 'password' => 'pass123']
    ];
    
    echo "<div class='table-responsive'>
    <table class='table table-striped'>
    <thead class='table-dark'>
    <tr>
    <th>اسم المستخدم</th>
    <th>كلمة المرور</th>
    <th>الإنشاء</th>
    <th>البحث</th>
    <th>تسجيل الدخول</th>
    </tr>
    </thead>
    <tbody>";
    
    foreach ($testCases as $index => $testCase) {
        $testUsername = $testCase['username'] . '_' . time() . '_' . $index;
        $testPassword = $testCase['password'];
        
        echo "<tr>
        <td><code>$testUsername</code></td>
        <td><code>$testPassword</code></td>";
        
        try {
            // إنشاء المستخدم
            $hashedPassword = password_hash($testPassword, PASSWORD_DEFAULT);
            $stmt = $db->query("INSERT INTO users (username, password, role, is_active) VALUES (?, ?, 'teacher', 1)", 
                              [$testUsername, $hashedPassword]);
            $userId = $db->lastInsertId();
            
            echo "<td><span class='badge bg-success'>نجح</span></td>";
            
            // البحث عن المستخدم
            $stmt = $db->query("SELECT * FROM users WHERE username = ?", [$testUsername]);
            $foundUser = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($foundUser) {
                echo "<td><span class='badge bg-success'>نجح</span></td>";
                
                // اختبار تسجيل الدخول
                if (password_verify($testPassword, $foundUser['password'])) {
                    echo "<td><span class='badge bg-success'>نجح</span></td>";
                } else {
                    echo "<td><span class='badge bg-danger'>فشل</span></td>";
                }
            } else {
                echo "<td><span class='badge bg-danger'>فشل</span></td>";
                echo "<td><span class='badge bg-secondary'>-</span></td>";
            }
            
        } catch (Exception $e) {
            echo "<td><span class='badge bg-danger'>فشل</span></td>";
            echo "<td><span class='badge bg-secondary'>-</span></td>";
            echo "<td><span class='badge bg-secondary'>-</span></td>";
        }
        
        echo "</tr>";
    }
    
    echo "</tbody></table></div>";
    
    echo "<div class='alert alert-info mt-3'>
    <i class='fas fa-info-circle me-2'></i>
    تم اختبار أنواع مختلفة من أسماء المستخدمين للتأكد من عمل الإصلاح.
    </div>";
}

echo "<form method='POST' class='mb-4'>
<button type='submit' name='comprehensive_test' class='btn btn-primary'>
<i class='fas fa-play me-2'></i>تشغيل الاختبار الشامل
</button>
</form>";

// نموذج اختبار يدوي
echo "<h5 class='text-primary'><i class='fas fa-hand-pointer me-2'></i>اختبار يدوي:</h5>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['manual_test'])) {
    $manualUsername = trim($_POST['manual_username']);
    $manualPassword = trim($_POST['manual_password']);
    
    echo "<div class='alert alert-info'>
    <strong>اختبار اسم المستخدم:</strong> '$manualUsername'<br>
    <strong>كلمة المرور:</strong> '$manualPassword'
    </div>";
    
    try {
        // فحص وجود اسم المستخدم
        $stmt = $db->query("SELECT COUNT(*) FROM users WHERE username = ?", [$manualUsername]);
        $exists = $stmt->fetchColumn();
        
        if ($exists > 0) {
            echo "<div class='alert alert-warning'>اسم المستخدم موجود بالفعل!</div>";
        } else {
            // إنشاء المستخدم
            $hashedPassword = password_hash($manualPassword, PASSWORD_DEFAULT);
            $stmt = $db->query("INSERT INTO users (username, password, role, is_active) VALUES (?, ?, 'teacher', 1)", 
                              [$manualUsername, $hashedPassword]);
            $userId = $db->lastInsertId();
            
            echo "<div class='alert alert-success'>تم إنشاء المستخدم بنجاح - ID: $userId</div>";
            
            // اختبار فوري
            $stmt = $db->query("SELECT * FROM users WHERE username = ?", [$manualUsername]);
            $testUser = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($testUser && password_verify($manualPassword, $testUser['password'])) {
                echo "<div class='alert alert-success'>
                <i class='fas fa-check me-2'></i>اختبار تسجيل الدخول نجح!<br>
                <a href='login.php' class='btn btn-success mt-2'>جرب تسجيل الدخول الآن</a>
                </div>";
            } else {
                echo "<div class='alert alert-danger'>اختبار تسجيل الدخول فشل!</div>";
            }
        }
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>خطأ: " . $e->getMessage() . "</div>";
    }
}

echo "<div class='card'>
<div class='card-header'>
<h6 class='mb-0'>اختبار إنشاء معلم يدوياً</h6>
</div>
<div class='card-body'>
<form method='POST'>
<div class='row'>
<div class='col-md-6 mb-3'>
<label for='manual_username' class='form-label'>اسم المستخدم:</label>
<input type='text' class='form-control' id='manual_username' name='manual_username' 
       placeholder='أدخل اسم المستخدم' required>
</div>
<div class='col-md-6 mb-3'>
<label for='manual_password' class='form-label'>كلمة المرور:</label>
<input type='text' class='form-control' id='manual_password' name='manual_password' 
       placeholder='أدخل كلمة المرور' required>
</div>
</div>
<button type='submit' name='manual_test' class='btn btn-success'>
<i class='fas fa-user-plus me-2'></i>إنشاء واختبار
</button>
</form>
</div>
</div>";

// النتيجة النهائية
echo "<div class='mt-4'>
<div class='alert alert-success alert-lg'>
<h4><i class='fas fa-check-circle me-2'></i>تم إصلاح المشكلة نهائياً!</h4>
<hr>
<p class='mb-0'>الآن عندما تنشئ معلم جديد مع اسم مستخدم وكلمة مرور، يجب أن تتمكن من تسجيل الدخول بهما بدون مشاكل.</p>
</div>
</div>";

// تعليمات للمستخدم
echo "<div class='alert alert-primary mt-4'>
<h5><i class='fas fa-lightbulb me-2'></i>تعليمات مهمة:</h5>
<ol class='mb-0'>
<li><strong>استخدم أسماء مستخدمين بسيطة:</strong> أحرف وأرقام فقط</li>
<li><strong>تجنب الرموز الخاصة:</strong> مثل @, #, %, إلخ</li>
<li><strong>يمكن استخدام:</strong> النقاط (.) والشرطات (-) والشرطات السفلية (_)</li>
<li><strong>تأكد من عدم وجود مسافات:</strong> في بداية أو نهاية اسم المستخدم</li>
</ol>
</div>";

// روابط سريعة
echo "<div class='alert alert-warning mt-4'>
<h5><i class='fas fa-link me-2'></i>روابط سريعة:</h5>
<div class='row'>
<div class='col-md-6'>
<ul class='list-unstyled'>
<li><a href='teachers/add.php' class='btn btn-sm btn-outline-success me-2 mb-2'><i class='fas fa-user-plus me-1'></i>إضافة معلم جديد</a></li>
<li><a href='login.php' class='btn btn-sm btn-outline-primary me-2 mb-2'><i class='fas fa-sign-in-alt me-1'></i>صفحة تسجيل الدخول</a></li>
</ul>
</div>
<div class='col-md-6'>
<ul class='list-unstyled'>
<li><a href='debug_username_issue.php' class='btn btn-sm btn-outline-warning me-2 mb-2'><i class='fas fa-bug me-1'></i>تشخيص اسم المستخدم</a></li>
<li><a href='index.php' class='btn btn-sm btn-outline-secondary me-2 mb-2'><i class='fas fa-home me-1'></i>الصفحة الرئيسية</a></li>
</ul>
</div>
</div>
</div>";

echo "</div>
</div>
</div>
</div>
</div>
</body>
</html>";
?>
