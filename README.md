# نظام أرشفة الأساتذة

نظام شامل لإدارة ومتابعة أعمال الأساتذة في المدرسة، مطور باستخدام PHP و SQLite و Bootstrap 5 RTL.

## 🌟 المميزات

### للإدارة (المدير):
- **إدارة المعلمين**: إضافة، تعديل، حذف، وعرض بيانات المعلمين مع البحث والتصفية
- **متابعة الحضور**: تسجيل ومتابعة حضور وغياب المعلمين مع إحصائيات مفصلة
- **التقارير الشاملة**: تقارير عن أداء المعلمين والأنشطة مع إمكانية التصدير
- **إدارة التنويهات**: إرسال تنويهات وإنذارات للمعلمين مع متابعة الردود
- **متابعة المنهج**: مراقبة تقدم المعلمين في المناهج المقررة
- **إدارة الأنشطة**: متابعة المسابقات والأنشطة المدرسية
- **لوحة تحكم شاملة**: عرض الإحصائيات والأنشطة الحديثة

### للمعلمين:
- **الملف الشخصي المتكامل**: إدارة البيانات الشخصية والمؤهلات والخبرات
- **التحضير اليومي**: إضافة وإدارة التحضير اليومي للدروس مع البحث والتصفية
- **الأنشطة والمسابقات**: إدارة المسابقات والأنشطة الخاصة مع متابعة المشاركين
- **متابعة المنهج**: تسجيل التقدم في المنهج المقرر مع مؤشرات التقدم
- **التنويهات والإنذارات**: استقبال ومتابعة التنويهات الإدارية مع إمكانية الرد
- **إدارة الملفات**: رفع وإدارة الوثائق والملفات المرتبطة بالعمل
- **لوحة تحكم شخصية**: عرض الإحصائيات الشخصية والمهام الحديثة

## 🛠️ التقنيات المستخدمة

- **Backend**: PHP 7.4+
- **Database**: SQLite
- **Frontend**: Bootstrap 5 RTL
- **Icons**: Font Awesome 6
- **Fonts**: Google Fonts (Cairo)
- **JavaScript**: Vanilla JS + Bootstrap JS

## 📋 متطلبات النظام

- PHP 7.4 أو أحدث
- SQLite3
- خادم ويب (Apache/Nginx)
- متصفح حديث يدعم HTML5 و CSS3

## 🚀 التثبيت والإعداد

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd school-archive-system
```

### 2. إعداد الخادم
- تأكد من تشغيل خادم الويب
- تأكد من تفعيل SQLite في PHP
- ضع المشروع في مجلد الخادم (htdocs/www)

### 3. إعداد قاعدة البيانات
- قم بزيارة `setup_demo_data.php` لإنشاء قاعدة البيانات والبيانات التجريبية
- أو قم بتشغيل الملف مباشرة من المتصفح

### 4. بيانات تسجيل الدخول الافتراضية

#### المدير:
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

#### المعلمين التجريبيين:
- **المعلم 1**: teacher1 / teacher123
- **المعلم 2**: teacher2 / teacher123
- **المعلم 3**: teacher3 / teacher123
- **المعلم 4**: teacher4 / teacher123

## 📁 هيكل المشروع

```
school-archive-system/
├── config/
│   ├── config.php          # الإعدادات العامة
│   └── database.php        # إعدادات قاعدة البيانات
├── assets/
│   ├── css/
│   │   └── style.css       # الأنماط المخصصة
│   └── js/
│       └── main.js         # JavaScript المخصص
├── teachers/
│   ├── list.php           # قائمة المعلمين
│   ├── add.php            # إضافة معلم
│   ├── edit.php           # تعديل معلم
│   └── view.php           # عرض تفاصيل معلم
├── preparations/
│   ├── my.php             # تحضيري اليومي
│   ├── add.php            # إضافة تحضير
│   ├── view.php           # عرض التحضير
│   └── delete.php         # حذف التحضير
├── activities/
│   ├── my.php             # أنشطتي ومسابقاتي
│   └── add.php            # إضافة نشاط
├── curriculum/
│   └── my.php             # متابعة المنهج
├── warnings/
│   └── my.php             # التنويهات والإنذارات
├── attendance/
│   └── today.php          # حضور اليوم
├── files/
│   ├── my.php             # ملفاتي
│   └── download.php       # تحميل الملفات
├── ajax/
│   └── update_attendance.php # تحديث الحضور
├── database/
│   └── school_archive.db   # قاعدة البيانات
├── uploads/               # الملفات المرفوعة
├── index.php             # الصفحة الرئيسية
├── login.php             # تسجيل الدخول
├── logout.php            # تسجيل الخروج
├── profile.php           # الملف الشخصي
├── setup_demo_data.php   # إعداد البيانات التجريبية
├── setup_fixed.php      # إعداد محدث ومُصحح
├── final_update.php     # التحديث النهائي
└── test_php.php         # اختبار PHP
```

## 🗄️ قاعدة البيانات

### الجداول الرئيسية:
- **users**: المستخدمين (مديرين ومعلمين)
- **teachers**: بيانات المعلمين التفصيلية
- **qualifications**: المؤهلات والشهادات
- **experiences**: الخبرات العملية
- **daily_preparations**: التحضير اليومي
- **activities**: الأنشطة والمسابقات
- **curriculum_progress**: متابعة المنهج
- **reports**: التقارير
- **warnings**: التنويهات والإنذارات
- **attendance**: الحضور والغياب
- **files**: الملفات المرفوعة

## 🎨 التصميم

- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **دعم RTL**: مصمم خصيصاً للغة العربية
- **ألوان حديثة**: تدرجات لونية جذابة
- **خط عربي واضح**: خط Cairo من Google Fonts
- **واجهة سهلة**: تصميم بديهي وسهل الاستخدام

## 🔒 الأمان

- **تشفير كلمات المرور**: باستخدام password_hash
- **حماية من SQL Injection**: استخدام Prepared Statements
- **حماية من XSS**: تنظيف البيانات المدخلة
- **إدارة الجلسات**: نظام جلسات آمن
- **صلاحيات المستخدمين**: تحكم في الوصول حسب الدور

## 📱 الاستجابة والتوافق

- متوافق مع جميع المتصفحات الحديثة
- تصميم متجاوب يعمل على الهواتف والأجهزة اللوحية
- دعم كامل للغة العربية واتجاه RTL
- سرعة تحميل عالية

## 🔧 التخصيص

### تغيير الألوان:
عدّل المتغيرات في `assets/css/style.css`:
```css
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    /* ... */
}
```

### إضافة تخصصات جديدة:
عدّل قائمة التخصصات في `teachers/add.php`

### تخصيص قاعدة البيانات:
عدّل `config/database.php` لإضافة جداول أو حقول جديدة

## 🚀 التشغيل

1. **تشغيل XAMPP**:
   - تأكد من تشغيل Apache و MySQL

2. **الوصول للنظام**:
   - افتح المتصفح واذهب إلى: `http://localhost/school-archive-system`

3. **إعداد البيانات التجريبية** (اختياري):
   - اذهب إلى: `http://localhost/school-archive-system/setup_fixed.php`
   - أو: `http://localhost/school-archive-system/final_update.php`

4. **تسجيل الدخول**:
   - **المدير**: admin / admin123
   - **معلم تجريبي**: teacher1 / teacher123

## 🔧 الصفحات الرئيسية

### للمديرين:
- `/index.php` - لوحة التحكم الرئيسية
- `/teachers/list.php` - إدارة المعلمين
- `/attendance/today.php` - متابعة الحضور

### للمعلمين:
- `/index.php` - لوحة التحكم الشخصية
- `/preparations/my.php` - التحضير اليومي
- `/activities/my.php` - الأنشطة والمسابقات
- `/curriculum/my.php` - متابعة المنهج
- `/warnings/my.php` - التنويهات والإنذارات
- `/files/my.php` - إدارة الملفات

## 🐛 الإبلاغ عن الأخطاء

إذا واجهت أي مشاكل أو أخطاء، يرجى:
1. التحقق من سجلات الأخطاء في الخادم
2. التأكد من صحة إعدادات قاعدة البيانات
3. التحقق من صلاحيات الملفات والمجلدات

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

## 👥 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## 📞 الدعم

للحصول على الدعم أو الاستفسارات:
- إنشاء Issue في المشروع
- مراجعة الوثائق
- فحص ملفات الأمثلة

---

**تم تطويره بـ ❤️ لخدمة التعليم**
