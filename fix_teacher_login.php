<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إصلاح مشكلة تسجيل دخول المعلمين</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap' rel='stylesheet'>
    <style>body { font-family: 'Cairo', sans-serif; }</style>
</head>
<body class='bg-light'>
<div class='container mt-5'>
<div class='row justify-content-center'>
<div class='col-md-10'>
<div class='card'>
<div class='card-header bg-success text-white'>
<h4 class='mb-0'><i class='fas fa-wrench me-2'></i>إصلاح مشكلة تسجيل دخول المعلمين</h4>
</div>
<div class='card-body'>";

echo "<div class='alert alert-info'>
<h5><i class='fas fa-info-circle me-2'></i>تشخيص وإصلاح مشكلة تسجيل الدخول</h5>
<p class='mb-0'>سيتم فحص وإصلاح جميع المشاكل المحتملة في تسجيل دخول المعلمين الجدد.</p>
</div>";

// فحص وإصلاح المستخدمين غير النشطين
echo "<h5 class='text-primary'><i class='fas fa-user-check me-2'></i>فحص وإصلاح المستخدمين غير النشطين:</h5>";

try {
    $stmt = $db->query("SELECT COUNT(*) FROM users WHERE is_active = 0 OR is_active IS NULL");
    $inactiveCount = $stmt->fetchColumn();
    
    if ($inactiveCount > 0) {
        echo "<div class='alert alert-warning'>يوجد $inactiveCount مستخدم غير نشط</div>";
        
        // تفعيل المستخدمين غير النشطين
        $stmt = $db->query("UPDATE users SET is_active = 1 WHERE is_active = 0 OR is_active IS NULL");
        $updatedCount = $stmt->rowCount();
        
        echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>تم تفعيل $updatedCount مستخدم</div>";
    } else {
        echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>جميع المستخدمين نشطين</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ في فحص المستخدمين: " . $e->getMessage() . "</div>";
}

// فحص المعلمين بدون حسابات مستخدمين
echo "<h5 class='text-primary mt-4'><i class='fas fa-user-times me-2'></i>فحص المعلمين بدون حسابات:</h5>";

try {
    $stmt = $db->query("SELECT t.* FROM teachers t 
                       LEFT JOIN users u ON t.user_id = u.id 
                       WHERE u.id IS NULL");
    $teachersWithoutUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($teachersWithoutUsers)) {
        echo "<div class='alert alert-warning'>يوجد " . count($teachersWithoutUsers) . " معلم بدون حساب مستخدم</div>";
        
        foreach ($teachersWithoutUsers as $teacher) {
            $username = strtolower($teacher['first_name'] . '.' . $teacher['last_name']);
            $username = preg_replace('/[^a-z0-9.]/', '', $username);
            if (strlen($username) < 3) {
                $username = 'teacher' . $teacher['employee_id'];
            }
            
            // التحقق من عدم تكرار اسم المستخدم
            $originalUsername = $username;
            $counter = 1;
            while (true) {
                $stmt = $db->query("SELECT COUNT(*) FROM users WHERE username = ?", [$username]);
                if ($stmt->fetchColumn() == 0) {
                    break;
                }
                $username = $originalUsername . $counter;
                $counter++;
            }
            
            $defaultPassword = 'teacher123';
            $hashedPassword = password_hash($defaultPassword, PASSWORD_DEFAULT);
            
            try {
                $db->getConnection()->beginTransaction();
                
                $stmt = $db->query("INSERT INTO users (username, password, role, is_active) VALUES (?, ?, 'teacher', 1)", 
                                  [$username, $hashedPassword]);
                $userId = $db->lastInsertId();
                
                $stmt = $db->query("UPDATE teachers SET user_id = ? WHERE id = ?", [$userId, $teacher['id']]);
                
                $db->getConnection()->commit();
                
                echo "<div class='alert alert-success alert-sm'>
                <i class='fas fa-check me-2'></i>
                تم إنشاء حساب للمعلم: <strong>{$teacher['first_name']} {$teacher['last_name']}</strong><br>
                <small>اسم المستخدم: <strong>$username</strong> | كلمة المرور: <strong>$defaultPassword</strong></small>
                </div>";
                
            } catch (Exception $e) {
                $db->getConnection()->rollBack();
                echo "<div class='alert alert-danger alert-sm'>خطأ في إنشاء حساب للمعلم {$teacher['first_name']} {$teacher['last_name']}: " . $e->getMessage() . "</div>";
            }
        }
    } else {
        echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>جميع المعلمين لديهم حسابات مستخدمين</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ في فحص المعلمين: " . $e->getMessage() . "</div>";
}

// اختبار شامل لتسجيل الدخول
echo "<h5 class='text-primary mt-4'><i class='fas fa-test-tube me-2'></i>اختبار شامل لتسجيل الدخول:</h5>";

try {
    $stmt = $db->query("SELECT u.*, t.first_name, t.last_name, t.employee_id 
                       FROM users u 
                       JOIN teachers t ON u.id = t.user_id 
                       WHERE u.role = 'teacher' 
                       ORDER BY u.created_at DESC 
                       LIMIT 5");
    $teacherUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($teacherUsers)) {
        echo "<div class='alert alert-warning'>لا يوجد معلمون مع حسابات مستخدمين</div>";
    } else {
        echo "<div class='table-responsive'>
        <table class='table table-striped'>
        <thead class='table-dark'>
        <tr>
        <th>اسم المعلم</th>
        <th>اسم المستخدم</th>
        <th>نشط</th>
        <th>تاريخ الإنشاء</th>
        <th>اختبار كلمة المرور</th>
        </tr>
        </thead>
        <tbody>";
        
        foreach ($teacherUsers as $user) {
            $teacherName = $user['first_name'] . ' ' . $user['last_name'];
            $isActive = $user['is_active'] ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-danger">غير نشط</span>';
            $createdAt = date('Y-m-d H:i', strtotime($user['created_at']));
            
            echo "<tr>
            <td><strong>" . htmlspecialchars($teacherName) . "</strong></td>
            <td>" . htmlspecialchars($user['username']) . "</td>
            <td>$isActive</td>
            <td>$createdAt</td>
            <td>
                <button class='btn btn-sm btn-outline-primary' onclick='testPassword(\"{$user['username']}\", \"teacher123\")'>
                    اختبار teacher123
                </button>
            </td>
            </tr>";
        }
        
        echo "</tbody></table></div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ في جلب بيانات المعلمين: " . $e->getMessage() . "</div>";
}

// إنشاء معلم تجريبي جديد
echo "<h5 class='text-primary mt-4'><i class='fas fa-plus-circle me-2'></i>إنشاء معلم تجريبي جديد:</h5>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_new_teacher'])) {
    $newUsername = 'newteacher_' . time();
    $newPassword = 'newpass123';
    $newEmployeeId = 'NEW' . time();
    
    try {
        $db->getConnection()->beginTransaction();
        
        // إنشاء المستخدم
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        $stmt = $db->query("INSERT INTO users (username, password, role, is_active) VALUES (?, ?, 'teacher', 1)", 
                          [$newUsername, $hashedPassword]);
        $userId = $db->lastInsertId();
        
        // إنشاء المعلم
        $stmt = $db->query("INSERT INTO teachers (user_id, employee_id, first_name, last_name, email, subject, grade_level, hire_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", 
                          [$userId, $newEmployeeId, 'معلم', 'جديد', '<EMAIL>', 'اختبار جديد', 'الصف الثاني', date('Y-m-d')]);
        
        $db->getConnection()->commit();
        
        echo "<div class='alert alert-success'>
        <h6><i class='fas fa-check me-2'></i>تم إنشاء معلم جديد بنجاح!</h6>
        <ul class='mb-0'>
        <li><strong>اسم المستخدم:</strong> $newUsername</li>
        <li><strong>كلمة المرور:</strong> $newPassword</li>
        <li><strong>رقم الموظف:</strong> $newEmployeeId</li>
        </ul>
        <div class='mt-3'>
        <button class='btn btn-primary' onclick='testPassword(\"$newUsername\", \"$newPassword\")'>
            اختبار تسجيل الدخول
        </button>
        <a href='login.php' class='btn btn-success ms-2'>الذهاب لصفحة تسجيل الدخول</a>
        </div>
        </div>";
        
    } catch (Exception $e) {
        $db->getConnection()->rollBack();
        echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ في إنشاء المعلم الجديد: " . $e->getMessage() . "</div>";
    }
}

echo "<form method='POST' class='mb-4'>
<button type='submit' name='create_new_teacher' class='btn btn-primary'>
<i class='fas fa-user-plus me-2'></i>إنشاء معلم تجريبي جديد
</button>
</form>";

// النتيجة النهائية
echo "<div class='mt-4'>
<div class='alert alert-success alert-lg'>
<h4><i class='fas fa-check-circle me-2'></i>تم إصلاح مشكلة تسجيل الدخول!</h4>
<hr>
<p class='mb-0'>الآن يجب أن يعمل تسجيل الدخول للمعلمين الجدد بشكل صحيح. تم تفعيل جميع الحسابات وإنشاء حسابات للمعلمين المفقودين.</p>
</div>
</div>";

// تعليمات للمستخدم
echo "<div class='alert alert-primary mt-4'>
<h5><i class='fas fa-lightbulb me-2'></i>تعليمات مهمة:</h5>
<ol class='mb-0'>
<li><strong>للمعلمين الجدد:</strong> كلمة المرور الافتراضية هي <code>teacher123</code></li>
<li><strong>اسم المستخدم:</strong> يتم إنشاؤه تلقائياً من الاسم الأول والأخير</li>
<li><strong>تفعيل الحساب:</strong> جميع الحسابات الجديدة مفعلة تلقائياً</li>
<li><strong>في حالة المشاكل:</strong> استخدم أزرار \"اختبار\" لفحص كلمة المرور</li>
</ol>
</div>";

// روابط سريعة
echo "<div class='alert alert-warning mt-4'>
<h5><i class='fas fa-link me-2'></i>روابط سريعة:</h5>
<div class='row'>
<div class='col-md-6'>
<ul class='list-unstyled'>
<li><a href='teachers/add.php' class='btn btn-sm btn-outline-success me-2 mb-2'><i class='fas fa-user-plus me-1'></i>إضافة معلم جديد</a></li>
<li><a href='login.php' class='btn btn-sm btn-outline-primary me-2 mb-2'><i class='fas fa-sign-in-alt me-1'></i>صفحة تسجيل الدخول</a></li>
</ul>
</div>
<div class='col-md-6'>
<ul class='list-unstyled'>
<li><a href='debug_teacher_creation.php' class='btn btn-sm btn-outline-warning me-2 mb-2'><i class='fas fa-bug me-1'></i>تشخيص إنشاء المعلم</a></li>
<li><a href='index.php' class='btn btn-sm btn-outline-secondary me-2 mb-2'><i class='fas fa-home me-1'></i>الصفحة الرئيسية</a></li>
</ul>
</div>
</div>
</div>";

echo "</div>
</div>
</div>
</div>
</div>

<script>
function testPassword(username, password) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = 'debug_teacher_creation.php';
    form.innerHTML = `
        <input type='hidden' name='test_login' value='1'>
        <input type='hidden' name='test_username' value='\${username}'>
        <input type='hidden' name='test_password' value='\${password}'>
    `;
    document.body.appendChild(form);
    form.submit();
}
</script>

</body>
</html>";
?>
