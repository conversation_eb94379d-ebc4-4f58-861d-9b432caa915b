<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إصلاح أعمدة قاعدة البيانات - نظام أرشفة الأساتذة</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap' rel='stylesheet'>
    <style>body { font-family: 'Cairo', sans-serif; }</style>
</head>
<body class='bg-light'>
<div class='container mt-5'>
<div class='row justify-content-center'>
<div class='col-md-10'>
<div class='card'>
<div class='card-header bg-success text-white'>
<h4 class='mb-0'><i class='fas fa-database me-2'></i>إصلاح أعمدة قاعدة البيانات</h4>
</div>
<div class='card-body'>";

echo "<div class='alert alert-info'>
<h5><i class='fas fa-info-circle me-2'></i>تم إصلاح مشكلة الأعمدة المفقودة</h5>
<p class='mb-0'>تم تحديث صفحات التعديل والعرض لتتوافق مع بنية قاعدة البيانات الصحيحة.</p>
</div>";

// فحص بنية جدول المعلمين
echo "<h5 class='text-primary'><i class='fas fa-table me-2'></i>بنية جدول المعلمين الحالية:</h5>";

try {
    $stmt = $db->query("PRAGMA table_info(teachers)");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='table-responsive'>
    <table class='table table-striped table-sm'>
    <thead class='table-dark'>
    <tr>
    <th>اسم العمود</th>
    <th>نوع البيانات</th>
    <th>مطلوب</th>
    <th>القيمة الافتراضية</th>
    </tr>
    </thead>
    <tbody>";
    
    foreach ($columns as $column) {
        $required = $column['notnull'] ? '<span class="badge bg-danger">مطلوب</span>' : '<span class="badge bg-secondary">اختياري</span>';
        $defaultValue = $column['dflt_value'] ?: '-';
        
        echo "<tr>
        <td><strong>{$column['name']}</strong></td>
        <td>{$column['type']}</td>
        <td>$required</td>
        <td>$defaultValue</td>
        </tr>";
    }
    
    echo "</tbody></table></div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ في فحص بنية الجدول: " . $e->getMessage() . "</div>";
}

// فحص الجداول المرتبطة
echo "<h5 class='text-primary mt-4'><i class='fas fa-link me-2'></i>الجداول المرتبطة للمؤهلات والخبرات:</h5>";

$relatedTables = [
    'qualifications' => 'جدول المؤهلات والشهادات',
    'experiences' => 'جدول الخبرات المهنية'
];

foreach ($relatedTables as $table => $description) {
    try {
        $stmt = $db->query("SELECT COUNT(*) FROM $table");
        $count = $stmt->fetchColumn();
        
        echo "<div class='alert alert-success'>
        <i class='fas fa-check me-2'></i>
        <strong>$description</strong> - يحتوي على $count سجل
        </div>";
        
        // عرض بنية الجدول
        $stmt = $db->query("PRAGMA table_info($table)");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='ms-4 mb-3'>
        <small class='text-muted'>الأعمدة: ";
        $columnNames = array_column($columns, 'name');
        echo implode(', ', $columnNames);
        echo "</small></div>";
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>
        <i class='fas fa-times me-2'></i>
        <strong>$description</strong> - خطأ: " . $e->getMessage() . "
        </div>";
    }
}

// عرض بيانات المعلمين مع المؤهلات والخبرات
echo "<h5 class='text-primary mt-4'><i class='fas fa-users me-2'></i>المعلمون مع المؤهلات والخبرات:</h5>";

try {
    $stmt = $db->query("SELECT t.*, 
                              (SELECT COUNT(*) FROM qualifications q WHERE q.teacher_id = t.id) as qualifications_count,
                              (SELECT COUNT(*) FROM experiences e WHERE e.teacher_id = t.id) as experiences_count
                       FROM teachers t 
                       ORDER BY t.first_name, t.last_name 
                       LIMIT 10");
    $teachers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($teachers)) {
        echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle me-2'></i>لا يوجد معلمون في النظام</div>";
    } else {
        echo "<div class='table-responsive'>
        <table class='table table-striped table-sm'>
        <thead class='table-dark'>
        <tr>
        <th>الاسم</th>
        <th>رقم الموظف</th>
        <th>المادة</th>
        <th>المؤهلات</th>
        <th>الخبرات</th>
        <th>الإجراءات</th>
        </tr>
        </thead>
        <tbody>";
        
        foreach ($teachers as $teacher) {
            $teacherName = $teacher['first_name'] . ' ' . $teacher['last_name'];
            $qualificationsCount = $teacher['qualifications_count'];
            $experiencesCount = $teacher['experiences_count'];
            
            echo "<tr>
            <td><strong>" . htmlspecialchars($teacherName) . "</strong></td>
            <td>" . htmlspecialchars($teacher['employee_id']) . "</td>
            <td>" . htmlspecialchars($teacher['subject']) . "</td>
            <td>
                <span class='badge bg-info'>$qualificationsCount مؤهل</span>
                " . ($qualificationsCount == 0 ? "<a href='add_qualification.php?teacher_id={$teacher['id']}' class='btn btn-sm btn-outline-primary ms-1'>إضافة</a>" : "") . "
            </td>
            <td>
                <span class='badge bg-warning'>$experiencesCount خبرة</span>
                " . ($experiencesCount == 0 ? "<a href='add_experience.php?teacher_id={$teacher['id']}' class='btn btn-sm btn-outline-success ms-1'>إضافة</a>" : "") . "
            </td>
            <td>
                <a href='teachers/view.php?id={$teacher['id']}' class='btn btn-sm btn-outline-primary' title='عرض'>
                    <i class='fas fa-eye'></i>
                </a>
                <a href='teachers/edit.php?id={$teacher['id']}' class='btn btn-sm btn-outline-warning' title='تعديل'>
                    <i class='fas fa-edit'></i>
                </a>
            </td>
            </tr>";
        }
        
        echo "</tbody></table></div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ في جلب بيانات المعلمين: " . $e->getMessage() . "</div>";
}

// الإصلاحات المطبقة
echo "<h5 class='text-primary mt-4'><i class='fas fa-wrench me-2'></i>الإصلاحات المطبقة:</h5>";

echo "<div class='row'>
<div class='col-md-6'>
<div class='card border-success'>
<div class='card-header bg-success text-white'>
<h6 class='mb-0'><i class='fas fa-check me-2'></i>تم إصلاحه</h6>
</div>
<div class='card-body'>
<ul class='mb-0'>
<li>إزالة الحقول غير الموجودة من نموذج التعديل</li>
<li>تحديث استعلام UPDATE ليتوافق مع بنية الجدول</li>
<li>إزالة الحقول غير الموجودة من صفحة العرض</li>
<li>التأكد من توافق جميع الصفحات مع قاعدة البيانات</li>
</ul>
</div>
</div>
</div>

<div class='col-md-6'>
<div class='card border-info'>
<div class='card-header bg-info text-white'>
<h6 class='mb-0'><i class='fas fa-info me-2'></i>البديل المتاح</h6>
</div>
<div class='card-body'>
<ul class='mb-0'>
<li>استخدام جدول <code>qualifications</code> للمؤهلات</li>
<li>استخدام جدول <code>experiences</code> للخبرات</li>
<li>إمكانية إضافة مؤهلات وخبرات متعددة لكل معلم</li>
<li>بنية أكثر مرونة ومطابقة للمعايير</li>
</ul>
</div>
</div>
</div>
</div>";

// النتيجة النهائية
echo "<div class='mt-4'>
<div class='alert alert-success alert-lg'>
<h4><i class='fas fa-check-circle me-2'></i>تم إصلاح المشكلة بنجاح!</h4>
<hr>
<p class='mb-0'>الآن يمكن تعديل بيانات المعلمين بدون أخطاء. تم تحديث جميع الصفحات لتتوافق مع بنية قاعدة البيانات الصحيحة.</p>
</div>
</div>";

// معلومات إضافية
echo "<div class='alert alert-primary mt-4'>
<h5><i class='fas fa-lightbulb me-2'></i>معلومات مهمة:</h5>
<ul class='mb-0'>
<li><strong>المؤهلات:</strong> يتم تخزينها في جدول منفصل يسمح بإضافة مؤهلات متعددة لكل معلم</li>
<li><strong>الخبرات:</strong> يتم تخزينها في جدول منفصل يسمح بإضافة خبرات متعددة لكل معلم</li>
<li><strong>المرونة:</strong> هذا التصميم أكثر مرونة ويتيح إدارة أفضل للبيانات</li>
<li><strong>التوسع:</strong> يمكن إضافة المزيد من التفاصيل لكل مؤهل أو خبرة</li>
</ul>
</div>";

// روابط سريعة للاختبار
echo "<div class='alert alert-warning mt-4'>
<h5><i class='fas fa-link me-2'></i>روابط سريعة للاختبار:</h5>
<div class='row'>
<div class='col-md-6'>
<ul class='list-unstyled'>
<li><a href='teachers/list.php' class='btn btn-sm btn-outline-primary me-2 mb-2'><i class='fas fa-users me-1'></i>قائمة المعلمين</a></li>
<li><a href='teachers/add.php' class='btn btn-sm btn-outline-success me-2 mb-2'><i class='fas fa-user-plus me-1'></i>إضافة معلم جديد</a></li>
</ul>
</div>
<div class='col-md-6'>
<ul class='list-unstyled'>
<li><a href='login.php' class='btn btn-sm btn-outline-info me-2 mb-2'><i class='fas fa-sign-in-alt me-1'></i>تسجيل الدخول</a></li>
<li><a href='index.php' class='btn btn-sm btn-outline-secondary me-2 mb-2'><i class='fas fa-home me-1'></i>الصفحة الرئيسية</a></li>
</ul>
</div>
</div>
</div>";

echo "</div>
</div>
</div>
</div>
</div>
</body>
</html>";
?>
