<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إصلاح حذف المعلمين - نظام أرشفة الأساتذة</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap' rel='stylesheet'>
    <style>body { font-family: 'Cairo', sans-serif; }</style>
</head>
<body class='bg-light'>
<div class='container mt-5'>
<div class='row justify-content-center'>
<div class='col-md-10'>
<div class='card'>
<div class='card-header bg-success text-white'>
<h4 class='mb-0'><i class='fas fa-wrench me-2'></i>إصلاح حذف المعلمين</h4>
</div>
<div class='card-body'>";

echo "<div class='alert alert-info'>
<h5><i class='fas fa-info-circle me-2'></i>تم إصلاح مشكلة حذف المعلمين</h5>
<p class='mb-0'>تم تحديث صفحة الحذف للتعامل مع جميع الجداول المرتبطة بشكل صحيح.</p>
</div>";

// فحص الجداول المرتبطة
echo "<h5 class='text-primary'><i class='fas fa-database me-2'></i>فحص الجداول المرتبطة:</h5>";

$tables = [
    'teachers' => 'جدول المعلمين الرئيسي',
    'users' => 'جدول المستخدمين',
    'daily_preparations' => 'التحضيرات اليومية',
    'activities' => 'الأنشطة والمسابقات',
    'curriculum_progress' => 'تقدم المنهج',
    'files' => 'الملفات المرفوعة',
    'warnings' => 'التنويهات',
    'qualifications' => 'المؤهلات العلمية',
    'experiences' => 'الخبرات المهنية'
];

foreach ($tables as $table => $description) {
    try {
        $stmt = $db->query("SELECT COUNT(*) FROM $table");
        $count = $stmt->fetchColumn();
        echo "<div class='alert alert-success alert-sm'><i class='fas fa-check me-2'></i><strong>$description</strong> - $count سجل</div>";
    } catch (Exception $e) {
        echo "<div class='alert alert-warning alert-sm'><i class='fas fa-exclamation-triangle me-2'></i><strong>$description</strong> - غير متوفر: " . $e->getMessage() . "</div>";
    }
}

// اختبار حذف تجريبي
echo "<h5 class='text-primary mt-4'><i class='fas fa-test-tube me-2'></i>اختبار وظيفة الحذف:</h5>";

try {
    // إنشاء معلم تجريبي للاختبار
    $testUsername = 'test_delete_' . time();
    $testPassword = password_hash('test123', PASSWORD_DEFAULT);
    
    // إضافة مستخدم تجريبي
    $stmt = $db->query("INSERT INTO users (username, password, role, is_active) VALUES (?, ?, 'teacher', 1)", 
                      [$testUsername, $testPassword]);
    $testUserId = $db->lastInsertId();
    
    // إضافة معلم تجريبي
    $stmt = $db->query("INSERT INTO teachers (user_id, employee_id, first_name, last_name, subject, grade_level, hire_date) VALUES (?, ?, ?, ?, ?, ?, ?)", 
                      [$testUserId, 'TEST' . time(), 'معلم', 'تجريبي', 'اختبار', 'الصف الأول', date('Y-m-d')]);
    $testTeacherId = $db->lastInsertId();
    
    echo "<div class='alert alert-info'><i class='fas fa-plus me-2'></i>تم إنشاء معلم تجريبي للاختبار (ID: $testTeacherId)</div>";
    
    // إضافة بيانات مرتبطة تجريبية
    $relatedData = [];
    
    // إضافة تحضير تجريبي
    try {
        $stmt = $db->query("INSERT INTO daily_preparations (teacher_id, subject, grade, lesson_title, lesson_objectives, lesson_content, preparation_date) VALUES (?, ?, ?, ?, ?, ?, ?)", 
                          [$testTeacherId, 'اختبار', 'الصف الأول', 'درس تجريبي', 'اختبار الحذف', 'محتوى تجريبي', date('Y-m-d')]);
        $relatedData[] = 'تحضير يومي';
    } catch (Exception $e) {
        echo "<div class='alert alert-warning alert-sm'>تعذر إضافة تحضير تجريبي: " . $e->getMessage() . "</div>";
    }
    
    // إضافة نشاط تجريبي
    try {
        $stmt = $db->query("INSERT INTO activities (teacher_id, title, description, activity_type, start_date, status) VALUES (?, ?, ?, ?, ?, ?)", 
                          [$testTeacherId, 'نشاط تجريبي', 'اختبار الحذف', 'activity', date('Y-m-d'), 'planned']);
        $relatedData[] = 'نشاط';
    } catch (Exception $e) {
        echo "<div class='alert alert-warning alert-sm'>تعذر إضافة نشاط تجريبي: " . $e->getMessage() . "</div>";
    }
    
    // إضافة مؤهل تجريبي
    try {
        $stmt = $db->query("INSERT INTO qualifications (teacher_id, degree_type, institution, major, graduation_year) VALUES (?, ?, ?, ?, ?)", 
                          [$testTeacherId, 'بكالوريوس', 'جامعة تجريبية', 'تخصص تجريبي', 2020]);
        $relatedData[] = 'مؤهل';
    } catch (Exception $e) {
        echo "<div class='alert alert-warning alert-sm'>تعذر إضافة مؤهل تجريبي: " . $e->getMessage() . "</div>";
    }
    
    echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>تم إضافة البيانات المرتبطة: " . implode(', ', $relatedData) . "</div>";
    
    // اختبار الحذف
    echo "<h6 class='text-danger mt-3'>اختبار عملية الحذف:</h6>";
    
    $db->getConnection()->beginTransaction();
    
    try {
        // حذف البيانات المرتبطة
        $deletedCounts = [];
        $relatedTables = [
            'daily_preparations' => 'التحضيرات',
            'activities' => 'الأنشطة', 
            'curriculum_progress' => 'المنهج',
            'files' => 'الملفات',
            'warnings' => 'التنويهات',
            'qualifications' => 'المؤهلات',
            'experiences' => 'الخبرات'
        ];
        
        foreach ($relatedTables as $table => $description) {
            try {
                $stmt = $db->query("DELETE FROM $table WHERE teacher_id = ?", [$testTeacherId]);
                $deletedRows = $stmt->rowCount();
                if ($deletedRows > 0) {
                    $deletedCounts[] = "$deletedRows من $description";
                }
            } catch (Exception $e) {
                echo "<div class='alert alert-warning alert-sm'>تعذر حذف من $table: " . $e->getMessage() . "</div>";
            }
        }
        
        // حذف المعلم
        $stmt = $db->query("DELETE FROM teachers WHERE id = ?", [$testTeacherId]);
        $deletedTeacher = $stmt->rowCount();
        
        // حذف المستخدم
        $stmt = $db->query("DELETE FROM users WHERE id = ?", [$testUserId]);
        $deletedUser = $stmt->rowCount();
        
        $db->getConnection()->commit();
        
        echo "<div class='alert alert-success'>
        <i class='fas fa-check me-2'></i>
        <strong>نجح اختبار الحذف!</strong><br>
        تم حذف: $deletedTeacher معلم، $deletedUser مستخدم";
        
        if (!empty($deletedCounts)) {
            echo "، " . implode(', ', $deletedCounts);
        }
        
        echo "</div>";
        
    } catch (Exception $e) {
        $db->getConnection()->rollBack();
        echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>فشل اختبار الحذف: " . $e->getMessage() . "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ في إنشاء البيانات التجريبية: " . $e->getMessage() . "</div>";
}

// الإصلاحات المطبقة
echo "<h5 class='text-primary mt-4'><i class='fas fa-tools me-2'></i>الإصلاحات المطبقة:</h5>";

echo "<div class='row'>
<div class='col-md-6'>
<div class='card border-success'>
<div class='card-header bg-success text-white'>
<h6 class='mb-0'><i class='fas fa-check me-2'></i>تم إصلاحه</h6>
</div>
<div class='card-body'>
<ul class='mb-0'>
<li>إضافة حذف جميع الجداول المرتبطة</li>
<li>معالجة الأخطاء للجداول غير الموجودة</li>
<li>تحسين رسائل الخطأ والنجاح</li>
<li>إضافة تسجيل الأحداث (logging)</li>
<li>التحقق من نجاح عملية الحذف</li>
</ul>
</div>
</div>
</div>

<div class='col-md-6'>
<div class='card border-info'>
<div class='card-header bg-info text-white'>
<h6 class='mb-0'><i class='fas fa-info me-2'></i>الجداول المشمولة</h6>
</div>
<div class='card-body'>
<ul class='mb-0'>
<li>daily_preparations (التحضيرات)</li>
<li>activities (الأنشطة)</li>
<li>curriculum_progress (المنهج)</li>
<li>files (الملفات)</li>
<li>warnings (التنويهات)</li>
<li>qualifications (المؤهلات)</li>
<li>experiences (الخبرات)</li>
</ul>
</div>
</div>
</div>
</div>";

// النتيجة النهائية
echo "<div class='mt-4'>
<div class='alert alert-success alert-lg'>
<h4><i class='fas fa-check-circle me-2'></i>تم إصلاح مشكلة حذف المعلمين!</h4>
<hr>
<p class='mb-0'>الآن يمكن حذف المعلمين بنجاح مع جميع البيانات المرتبطة بهم. تم تحسين عملية الحذف لتكون أكثر أماناً وشمولية.</p>
</div>
</div>";

// روابط سريعة للاختبار
echo "<div class='alert alert-warning mt-4'>
<h5><i class='fas fa-link me-2'></i>روابط سريعة للاختبار:</h5>
<div class='row'>
<div class='col-md-6'>
<ul class='list-unstyled'>
<li><a href='teachers/list.php' class='btn btn-sm btn-outline-primary me-2 mb-2'><i class='fas fa-users me-1'></i>قائمة المعلمين</a></li>
<li><a href='debug_delete_teacher.php' class='btn btn-sm btn-outline-warning me-2 mb-2'><i class='fas fa-bug me-1'></i>تشخيص الحذف</a></li>
</ul>
</div>
<div class='col-md-6'>
<ul class='list-unstyled'>
<li><a href='teachers/add.php' class='btn btn-sm btn-outline-success me-2 mb-2'><i class='fas fa-user-plus me-1'></i>إضافة معلم جديد</a></li>
<li><a href='index.php' class='btn btn-sm btn-outline-secondary me-2 mb-2'><i class='fas fa-home me-1'></i>الصفحة الرئيسية</a></li>
</ul>
</div>
</div>
</div>";

echo "</div>
</div>
</div>
</div>
</div>
</body>
</html>";
?>
