<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار تسجيل الدخول النهائي</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <style>body { font-family: Arial, sans-serif; }</style>
</head>
<body class='bg-light'>
<div class='container mt-5'>
<div class='card'>
<div class='card-header bg-success text-white'>
<h3>🔧 اختبار تسجيل الدخول النهائي - D:\arch</h3>
</div>
<div class='card-body'>";

// إنشاء مستخدم تجريبي فوري
if (isset($_GET['create'])) {
    $testUser = 'finaltest' . time();
    $testPass = 'final123';
    
    try {
        $db->getConnection()->beginTransaction();
        
        $hashedPassword = password_hash($testPass, PASSWORD_DEFAULT);
        $stmt = $db->query("INSERT INTO users (username, password, role, is_active) VALUES (?, ?, 'teacher', 1)", 
                          [$testUser, $hashedPassword]);
        $userId = $db->lastInsertId();
        
        $stmt = $db->query("INSERT INTO teachers (user_id, employee_id, first_name, last_name, email, subject, grade_level, hire_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", 
                          [$userId, 'FINAL' . time(), 'معلم', 'نهائي', '<EMAIL>', 'اختبار', 'الصف الأول', date('Y-m-d')]);
        
        $db->getConnection()->commit();
        
        echo "<div class='alert alert-success'>
        <h4>✅ تم إنشاء مستخدم تجريبي بنجاح!</h4>
        <div style='background: yellow; padding: 10px; border-radius: 5px; margin: 10px 0;'>
        <p><strong>اسم المستخدم:</strong> <code style='font-size: 18px; font-weight: bold;'>$testUser</code></p>
        <p><strong>كلمة المرور:</strong> <code style='font-size: 18px; font-weight: bold;'>$testPass</code></p>
        </div>
        <p><strong>انسخ هذه البيانات واستخدمها في النموذج أدناه ↓</strong></p>
        </div>";
        
    } catch (Exception $e) {
        $db->getConnection()->rollBack();
        echo "<div class='alert alert-danger'>خطأ: " . $e->getMessage() . "</div>";
    }
}

// اختبار تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    echo "<div class='alert alert-info'>
    <h5>📊 تشخيص مباشر:</h5>
    <p><strong>اسم المستخدم:</strong> '<span style='background: yellow; padding: 2px;'>$username</span>' (طول: " . strlen($username) . ")</p>
    <p><strong>كلمة المرور:</strong> '<span style='background: yellow; padding: 2px;'>$password</span>' (طول: " . strlen($password) . ")</p>
    </div>";
    
    if (empty($username) || empty($password)) {
        echo "<div class='alert alert-danger'>❌ البيانات فارغة!</div>";
    } else {
        try {
            echo "<div class='alert alert-secondary'>
            <strong>الاستعلام:</strong> SELECT * FROM users WHERE username = '$username' AND is_active = 1
            </div>";
            
            $stmt = $db->query("SELECT * FROM users WHERE username = ? AND is_active = 1", [$username]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user) {
                echo "<div class='alert alert-success'>
                <h6>✅ تم العثور على المستخدم!</h6>
                <table class='table table-sm table-bordered'>
                <tr><td><strong>ID:</strong></td><td>{$user['id']}</td></tr>
                <tr><td><strong>اسم المستخدم:</strong></td><td><code>{$user['username']}</code></td></tr>
                <tr><td><strong>الدور:</strong></td><td>{$user['role']}</td></tr>
                <tr><td><strong>نشط:</strong></td><td>" . ($user['is_active'] ? 'نعم' : 'لا') . "</td></tr>
                <tr><td><strong>تاريخ الإنشاء:</strong></td><td>{$user['created_at']}</td></tr>
                </table>
                </div>";
                
                if (password_verify($password, $user['password'])) {
                    echo "<div class='alert alert-success'>
                    <h4>🎉 تسجيل الدخول نجح!</h4>
                    <p>✅ كلمة المرور صحيحة!</p>
                    <p>✅ المستخدم نشط!</p>
                    <p>✅ جميع الشروط مستوفاة!</p>
                    <hr>
                    <h5>الآن جرب في صفحة تسجيل الدخول الحقيقية:</h5>
                    <a href='login.php' class='btn btn-success btn-lg'>صفحة تسجيل الدخول</a>
                    <a href='teachers/add.php' class='btn btn-primary btn-lg ms-2'>إضافة معلم جديد</a>
                    </div>";
                } else {
                    echo "<div class='alert alert-danger'>❌ كلمة المرور خاطئة!</div>";
                    
                    // اختبار كلمات مرور مختلفة
                    echo "<div class='alert alert-warning'>
                    <h6>اختبار كلمات مرور مختلفة:</h6>";
                    $testPasswords = ['final123', 'test123', 'teacher123', 'debug123'];
                    foreach ($testPasswords as $testPass) {
                        if (password_verify($testPass, $user['password'])) {
                            echo "<p style='color: green;'>✅ كلمة المرور الصحيحة: '<strong>$testPass</strong>'</p>";
                            break;
                        } else {
                            echo "<p style='color: red;'>❌ ليست: '$testPass'</p>";
                        }
                    }
                    echo "</div>";
                }
            } else {
                echo "<div class='alert alert-danger'>❌ لم يتم العثور على المستخدم!</div>";
                
                // البحث بدون شرط النشاط
                $stmt = $db->query("SELECT * FROM users WHERE username = ?", [$username]);
                $userAny = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($userAny) {
                    echo "<div class='alert alert-warning'>
                    ⚠️ المستخدم موجود لكنه غير نشط!<br>
                    is_active = {$userAny['is_active']}<br>
                    سيتم تفعيله الآن...
                    </div>";
                    
                    $stmt = $db->query("UPDATE users SET is_active = 1 WHERE username = ?", [$username]);
                    echo "<div class='alert alert-success'>✅ تم تفعيل المستخدم! جرب مرة أخرى.</div>";
                } else {
                    echo "<div class='alert alert-danger'>❌ المستخدم غير موجود نهائياً!</div>";
                    
                    // عرض المستخدمين المتاحين
                    $stmt = $db->query("SELECT username FROM users WHERE role = 'teacher' LIMIT 5");
                    $availableUsers = $stmt->fetchAll(PDO::FETCH_COLUMN);
                    
                    if (!empty($availableUsers)) {
                        echo "<div class='alert alert-info'>
                        <h6>المستخدمون المتاحون:</h6>";
                        foreach ($availableUsers as $availableUser) {
                            echo "<p>- <code>$availableUser</code></p>";
                        }
                        echo "</div>";
                    }
                }
            }
            
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>❌ خطأ: " . $e->getMessage() . "</div>";
        }
    }
}

echo "<div class='row'>
<div class='col-md-6'>
<div class='card border-success'>
<div class='card-header bg-success text-white'>
<h5>1️⃣ إنشاء مستخدم تجريبي</h5>
</div>
<div class='card-body text-center'>
<p>أنشئ مستخدم تجريبي جديد للاختبار</p>
<a href='?create=1' class='btn btn-success btn-lg'>إنشاء مستخدم تجريبي</a>
</div>
</div>
</div>

<div class='col-md-6'>
<div class='card border-primary'>
<div class='card-header bg-primary text-white'>
<h5>2️⃣ اختبار تسجيل الدخول</h5>
</div>
<div class='card-body'>
<form method='POST'>
<div class='mb-3'>
<label class='form-label'>اسم المستخدم:</label>
<input type='text' class='form-control' name='username' required>
</div>
<div class='mb-3'>
<label class='form-label'>كلمة المرور:</label>
<input type='text' class='form-control' name='password' required>
</div>
<button type='submit' class='btn btn-primary w-100'>اختبار تسجيل الدخول</button>
</form>
</div>
</div>
</div>
</div>";

// عرض المستخدمين الحاليين
echo "<div class='mt-4'>
<h5>👥 المستخدمون الحاليون في D:\arch:</h5>";

try {
    $stmt = $db->query("SELECT username, is_active, created_at FROM users WHERE role = 'teacher' ORDER BY created_at DESC LIMIT 5");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($users)) {
        echo "<div class='table-responsive'>
        <table class='table table-striped'>
        <thead class='table-dark'>
        <tr><th>اسم المستخدم</th><th>نشط</th><th>تاريخ الإنشاء</th></tr>
        </thead>
        <tbody>";
        
        foreach ($users as $user) {
            $status = $user['is_active'] ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-danger">غير نشط</span>';
            $created = date('Y-m-d H:i', strtotime($user['created_at']));
            echo "<tr>
            <td><code>{$user['username']}</code></td>
            <td>$status</td>
            <td>$created</td>
            </tr>";
        }
        
        echo "</tbody></table></div>";
    } else {
        echo "<div class='alert alert-warning'>لا يوجد معلمون في النظام</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>خطأ في جلب المستخدمين: " . $e->getMessage() . "</div>";
}

echo "</div>";

echo "<div class='alert alert-primary mt-4'>
<h5>📋 تعليمات:</h5>
<ol>
<li><strong>اضغط على 'إنشاء مستخدم تجريبي'</strong> في الجانب الأيسر</li>
<li><strong>انسخ اسم المستخدم وكلمة المرور</strong> التي ستظهر باللون الأصفر</li>
<li><strong>ضعهما في النموذج</strong> في الجانب الأيمن</li>
<li><strong>اضغط 'اختبار تسجيل الدخول'</strong></li>
<li><strong>إذا نجح الاختبار</strong> - اذهب لصفحة تسجيل الدخول الحقيقية</li>
</ol>
</div>";

echo "<div class='alert alert-success mt-4'>
<h5>✅ التعديلات المطبقة في D:\arch:</h5>
<ul>
<li>✅ تم تعديل <code>login.php</code> - السطر 13</li>
<li>✅ تم تعديل <code>teachers/add.php</code> - السطر 14</li>
<li>✅ كلاهما يستخدم <code>trim()</code> فقط بدلاً من <code>sanitize()</code></li>
</ul>
</div>";

echo "</div>
</div>
</div>
</body>
</html>";
?>
