/* نظام أرشفة الأساتذة - ملف الأنماط المخصص */

/* الخط الأساسي */
body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    line-height: 1.6;
}

/* الألوان المخصصة */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --gradient-danger: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
}

/* تخصيص Bootstrap */
.bg-primary {
    background: var(--gradient-primary) !important;
}

.bg-gradient-primary {
    background: var(--gradient-primary) !important;
}

.bg-gradient-success {
    background: var(--gradient-success) !important;
}

.bg-gradient-warning {
    background: var(--gradient-warning) !important;
}

.bg-gradient-danger {
    background: var(--gradient-danger) !important;
}

/* شريط التنقل */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-bottom: 3px solid #3498db;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 5px;
    margin: 0 2px;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255,255,255,0.1);
    transform: translateY(-1px);
}

/* البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-header {
    background: var(--gradient-primary);
    color: white;
    border-bottom: none;
    font-weight: 600;
}

/* الأزرار */
.btn {
    border-radius: 25px;
    font-weight: 500;
    padding: 8px 20px;
    transition: all 0.3s ease;
    border: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn-primary {
    background: var(--gradient-primary);
}

.btn-success {
    background: var(--gradient-success);
}

.btn-warning {
    background: var(--gradient-warning);
}

.btn-danger {
    background: var(--gradient-danger);
}

/* الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background: var(--gradient-primary);
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.1);
    transform: scale(1.01);
}

/* النماذج */
.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
}

.form-control:focus, .form-select:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    transform: translateY(-1px);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: 10px;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.alert-success {
    background: var(--gradient-success);
    color: white;
}

.alert-danger {
    background: var(--gradient-danger);
    color: white;
}

.alert-warning {
    background: var(--gradient-warning);
    color: white;
}

.alert-info {
    background: var(--gradient-primary);
    color: white;
}

/* الشارات */
.badge {
    font-size: 0.8rem;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 500;
}

/* الأيقونات */
.fa, .fas, .far, .fab {
    transition: all 0.3s ease;
}

.card-body .fa-3x:hover {
    transform: scale(1.1) rotate(5deg);
}

/* تأثيرات الحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeInUp 0.6s ease-out;
}

/* تخصيص القوائم المنسدلة */
.dropdown-menu {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    padding: 10px 0;
}

.dropdown-item {
    padding: 8px 20px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.dropdown-item:hover {
    background: var(--gradient-primary);
    color: white;
    transform: translateX(5px);
}

/* تخصيص الصفحات */
.page-header {
    background: var(--gradient-primary);
    color: white;
    padding: 40px 0;
    margin-bottom: 30px;
    border-radius: 0 0 20px 20px;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* تخصيص الإحصائيات */
.stats-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stats-number {
    font-size: 3rem;
    font-weight: 700;
    margin: 15px 0;
}

.stats-label {
    font-size: 1.1rem;
    color: #6c757d;
    font-weight: 500;
}

/* تخصيص الملف الشخصي */
.profile-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 5px solid #3498db;
    margin: 0 auto 20px;
    object-fit: cover;
}

.profile-name {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 10px;
}

.profile-role {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 20px;
}

/* تخصيص الجدول الزمني */
.timeline {
    position: relative;
    padding: 20px 0;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 0;
    right: 50%;
    width: 2px;
    height: 100%;
    background: var(--gradient-primary);
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.timeline-item::before {
    content: '';
    position: absolute;
    top: 20px;
    right: -6px;
    width: 12px;
    height: 12px;
    background: #3498db;
    border-radius: 50%;
}

/* تخصيص الطباعة */
@media print {
    .navbar, .btn, .dropdown {
        display: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
    
    body {
        background: white !important;
    }
}

/* تخصيص الشاشات الصغيرة */
@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .card {
        margin-bottom: 20px;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}

/* تأثيرات إضافية */
.hover-shadow:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    transform: translateY(-2px);
}

.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* تخصيص رفع الملفات */
.file-upload-area {
    border: 2px dashed #3498db;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #2980b9;
    background-color: rgba(52, 152, 219, 0.1);
}

.file-upload-area.dragover {
    border-color: #27ae60;
    background-color: rgba(39, 174, 96, 0.1);
}

/* تخصيص التقويم */
.calendar {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    overflow: hidden;
}

.calendar-header {
    background: var(--gradient-primary);
    color: white;
    padding: 20px;
    text-align: center;
    font-weight: 600;
}

.calendar-day {
    padding: 15px;
    border: 1px solid #e9ecef;
    min-height: 80px;
    transition: all 0.3s ease;
}

.calendar-day:hover {
    background-color: rgba(52, 152, 219, 0.1);
}

.calendar-event {
    background: var(--gradient-success);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    margin: 2px 0;
}
