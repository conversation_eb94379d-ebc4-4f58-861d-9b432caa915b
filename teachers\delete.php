<?php
require_once '../config/config.php';

// التحقق من تسجيل الدخول وصلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    redirect('../login.php');
}

// الحصول على معرف المعلم
$teacherId = (int)($_GET['id'] ?? 0);
if ($teacherId <= 0) {
    showMessage('معرف المعلم غير صحيح', 'error');
    redirect('list.php');
}

// جلب بيانات المعلم للتأكد من وجوده
try {
    $stmt = $db->query("SELECT t.*, u.id as user_id, u.username 
                       FROM teachers t 
                       LEFT JOIN users u ON t.user_id = u.id 
                       WHERE t.id = ?", [$teacherId]);
    $teacher = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$teacher) {
        showMessage('لم يتم العثور على المعلم المطلوب', 'error');
        redirect('list.php');
    }
    
    // التحقق من وجود بيانات مرتبطة بالمعلم
    $relatedData = [];
    
    // التحضيرات
    $stmt = $db->query("SELECT COUNT(*) FROM daily_preparations WHERE teacher_id = ?", [$teacherId]);
    $preparationsCount = $stmt->fetchColumn();
    if ($preparationsCount > 0) {
        $relatedData[] = "$preparationsCount تحضير يومي";
    }
    
    // الأنشطة
    $stmt = $db->query("SELECT COUNT(*) FROM activities WHERE teacher_id = ?", [$teacherId]);
    $activitiesCount = $stmt->fetchColumn();
    if ($activitiesCount > 0) {
        $relatedData[] = "$activitiesCount نشاط ومسابقة";
    }
    
    // وحدات المنهج
    $stmt = $db->query("SELECT COUNT(*) FROM curriculum_progress WHERE teacher_id = ?", [$teacherId]);
    $curriculumCount = $stmt->fetchColumn();
    if ($curriculumCount > 0) {
        $relatedData[] = "$curriculumCount وحدة منهج";
    }
    
    // الملفات
    $stmt = $db->query("SELECT COUNT(*) FROM files WHERE teacher_id = ?", [$teacherId]);
    $filesCount = $stmt->fetchColumn();
    if ($filesCount > 0) {
        $relatedData[] = "$filesCount ملف";
    }
    
    // التنويهات
    $stmt = $db->query("SELECT COUNT(*) FROM warnings WHERE teacher_id = ?", [$teacherId]);
    $warningsCount = $stmt->fetchColumn();
    if ($warningsCount > 0) {
        $relatedData[] = "$warningsCount تنويه";
    }
    
    // حذف المعلم والبيانات المرتبطة
    $db->getConnection()->beginTransaction();
    
    try {
        // حذف البيانات المرتبطة أولاً
        $db->query("DELETE FROM daily_preparations WHERE teacher_id = ?", [$teacherId]);
        $db->query("DELETE FROM activities WHERE teacher_id = ?", [$teacherId]);
        $db->query("DELETE FROM curriculum_progress WHERE teacher_id = ?", [$teacherId]);
        $db->query("DELETE FROM files WHERE teacher_id = ?", [$teacherId]);
        $db->query("DELETE FROM warnings WHERE teacher_id = ?", [$teacherId]);
        
        // حذف المعلم
        $stmt = $db->query("DELETE FROM teachers WHERE id = ?", [$teacherId]);
        
        // حذف حساب المستخدم إذا كان موجوداً
        if ($teacher['user_id']) {
            $db->query("DELETE FROM users WHERE id = ?", [$teacher['user_id']]);
        }
        
        $db->getConnection()->commit();
        
        $teacherName = $teacher['first_name'] . ' ' . $teacher['last_name'];
        $message = "تم حذف المعلم \"$teacherName\" بنجاح";
        
        if (!empty($relatedData)) {
            $message .= " مع البيانات المرتبطة: " . implode(', ', $relatedData);
        }
        
        showMessage($message, 'success');
        redirect('list.php');
        
    } catch (Exception $e) {
        $db->getConnection()->rollBack();
        showMessage('خطأ في حذف المعلم: ' . $e->getMessage(), 'error');
        redirect('list.php');
    }
    
} catch (Exception $e) {
    showMessage('خطأ في الوصول لبيانات المعلم: ' . $e->getMessage(), 'error');
    redirect('list.php');
}
?>
