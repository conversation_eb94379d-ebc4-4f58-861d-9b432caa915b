<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إصلاح روابط الأنشطة - نظام أرشفة الأساتذة</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap' rel='stylesheet'>
    <style>body { font-family: 'Cairo', sans-serif; }</style>
</head>
<body class='bg-light'>
<div class='container mt-5'>
<div class='row justify-content-center'>
<div class='col-md-10'>
<div class='card'>
<div class='card-header bg-success text-white'>
<h4 class='mb-0'><i class='fas fa-check-circle me-2'></i>إصلاح روابط الأنشطة والمسابقات</h4>
</div>
<div class='card-body'>";

echo "<div class='alert alert-info'>
<h5><i class='fas fa-info-circle me-2'></i>تم إصلاح مشكلة روابط الأنشطة</h5>
<p class='mb-0'>تم إنشاء جميع الصفحات المطلوبة لنظام الأنشطة والمسابقات.</p>
</div>";

// فحص الصفحات المطلوبة
echo "<h5 class='text-primary'><i class='fas fa-check-circle me-2'></i>الصفحات المتاحة الآن:</h5>";

$activityPages = [
    'activities/list.php' => 'قائمة جميع الأنشطة (للمديرين)',
    'activities/my.php' => 'أنشطة المعلم الشخصية',
    'activities/add.php' => 'إضافة نشاط جديد',
    'activities/view.php' => 'عرض تفاصيل النشاط',
    'activities/edit.php' => 'تعديل النشاط',
    'activities/delete.php' => 'حذف النشاط'
];

foreach ($activityPages as $page => $description) {
    if (file_exists($page)) {
        echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i><strong>$description</strong> - $page متوفر</div>";
    } else {
        echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i><strong>$description</strong> - $page غير متوفر</div>";
    }
}

// فحص الروابط في الصفحة الرئيسية
echo "<h5 class='text-primary mt-4'><i class='fas fa-link me-2'></i>فحص الروابط في الصفحة الرئيسية:</h5>";

if (file_exists('index.php')) {
    $indexContent = file_get_contents('index.php');
    
    // فحص رابط الأنشطة النشطة للمديرين
    if (strpos($indexContent, 'activities/list.php') !== false) {
        echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>رابط \"الأنشطة النشطة\" للمديرين يؤدي إلى activities/list.php</div>";
    } else {
        echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle me-2'></i>رابط \"الأنشطة النشطة\" للمديرين غير موجود</div>";
    }
    
    // فحص رابط أنشطة المعلم
    if (strpos($indexContent, 'activities/my.php') !== false) {
        echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>رابط \"أنشطتي\" للمعلمين يؤدي إلى activities/my.php</div>";
    } else {
        echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle me-2'></i>رابط \"أنشطتي\" للمعلمين غير موجود</div>";
    }
}

// إضافة بيانات تجريبية إضافية للأنشطة
echo "<h5 class='text-primary mt-4'><i class='fas fa-plus-circle me-2'></i>إضافة بيانات تجريبية للأنشطة:</h5>";

try {
    // التحقق من وجود أنشطة
    $stmt = $db->query('SELECT COUNT(*) FROM activities');
    $activityCount = $stmt->fetchColumn();
    
    if ($activityCount < 5) {
        // إضافة أنشطة تجريبية إضافية
        $sampleActivities = [
            [
                'title' => 'مسابقة الخط العربي',
                'description' => 'مسابقة لأجمل خط عربي بين طلاب المدرسة',
                'activity_type' => 'competition',
                'target_grade' => 'الصف الخامس',
                'status' => 'ongoing',
                'participants_count' => 25
            ],
            [
                'title' => 'معرض العلوم والتكنولوجيا',
                'description' => 'معرض لعرض مشاريع الطلاب العلمية والتكنولوجية',
                'activity_type' => 'activity',
                'target_grade' => 'الصف السادس',
                'status' => 'planned',
                'participants_count' => 40
            ],
            [
                'title' => 'مشروع البيئة المدرسية',
                'description' => 'مشروع لتحسين البيئة المدرسية وزراعة الحديقة',
                'activity_type' => 'project',
                'target_grade' => 'جميع الصفوف',
                'status' => 'ongoing',
                'participants_count' => 60
            ]
        ];
        
        // الحصول على معلمين لربط الأنشطة بهم
        $stmt = $db->query('SELECT id FROM teachers LIMIT 3');
        $teachers = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!empty($teachers)) {
            foreach ($sampleActivities as $index => $activity) {
                $teacherId = $teachers[$index % count($teachers)];
                $startDate = date('Y-m-d', strtotime('+' . rand(1, 30) . ' days'));
                
                $stmt = $db->query(
                    'INSERT INTO activities (teacher_id, title, description, activity_type, target_grade, start_date, status, participants_count, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
                    [
                        $teacherId,
                        $activity['title'],
                        $activity['description'],
                        $activity['activity_type'],
                        $activity['target_grade'],
                        $startDate,
                        $activity['status'],
                        $activity['participants_count'],
                        date('Y-m-d H:i:s')
                    ]
                );
            }
            
            echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>تم إضافة " . count($sampleActivities) . " أنشطة تجريبية جديدة</div>";
        } else {
            echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle me-2'></i>لا يوجد معلمون لربط الأنشطة بهم</div>";
        }
    } else {
        echo "<div class='alert alert-info'><i class='fas fa-info me-2'></i>يوجد بالفعل $activityCount نشاط في النظام</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ في إضافة البيانات التجريبية: " . $e->getMessage() . "</div>";
}

// عرض إحصائيات الأنشطة الحالية
echo "<h5 class='text-primary mt-4'><i class='fas fa-chart-bar me-2'></i>إحصائيات الأنشطة الحالية:</h5>";

try {
    $stmt = $db->query('SELECT COUNT(*) FROM activities');
    $totalActivities = $stmt->fetchColumn();
    
    $stmt = $db->query('SELECT COUNT(*) FROM activities WHERE status = \"ongoing\"');
    $ongoingActivities = $stmt->fetchColumn();
    
    $stmt = $db->query('SELECT COUNT(*) FROM activities WHERE status = \"completed\"');
    $completedActivities = $stmt->fetchColumn();
    
    $stmt = $db->query('SELECT COUNT(*) FROM activities WHERE status = \"planned\"');
    $plannedActivities = $stmt->fetchColumn();
    
    echo "<div class='row'>
    <div class='col-md-3'>
        <div class='card text-center'>
            <div class='card-body'>
                <h4 class='text-primary'>$totalActivities</h4>
                <p class='mb-0'>إجمالي الأنشطة</p>
            </div>
        </div>
    </div>
    <div class='col-md-3'>
        <div class='card text-center'>
            <div class='card-body'>
                <h4 class='text-warning'>$ongoingActivities</h4>
                <p class='mb-0'>جارية حالياً</p>
            </div>
        </div>
    </div>
    <div class='col-md-3'>
        <div class='card text-center'>
            <div class='card-body'>
                <h4 class='text-success'>$completedActivities</h4>
                <p class='mb-0'>مكتملة</p>
            </div>
        </div>
    </div>
    <div class='col-md-3'>
        <div class='card text-center'>
            <div class='card-body'>
                <h4 class='text-info'>$plannedActivities</h4>
                <p class='mb-0'>مخططة</p>
            </div>
        </div>
    </div>
    </div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>خطأ في جلب الإحصائيات: " . $e->getMessage() . "</div>";
}

// النتيجة النهائية
echo "<div class='mt-4'>
<div class='alert alert-success alert-lg'>
<h4><i class='fas fa-check-circle me-2'></i>تم إصلاح المشكلة بنجاح!</h4>
<hr>
<p class='mb-0'>الآن يمكن الوصول لجميع صفحات الأنشطة والمسابقات بدون مشاكل. جميع الروابط تعمل بشكل صحيح.</p>
</div>
</div>";

// روابط سريعة للاختبار
echo "<div class='alert alert-primary mt-4'>
<h5><i class='fas fa-link me-2'></i>روابط سريعة لاختبار الأنشطة:</h5>
<div class='row'>
<div class='col-md-6'>
<ul class='list-unstyled'>
<li><a href='activities/list.php' class='btn btn-sm btn-outline-primary me-2 mb-2'><i class='fas fa-list me-1'></i>قائمة جميع الأنشطة</a></li>
<li><a href='activities/my.php' class='btn btn-sm btn-outline-success me-2 mb-2'><i class='fas fa-user me-1'></i>أنشطتي</a></li>
<li><a href='activities/add.php' class='btn btn-sm btn-outline-warning me-2 mb-2'><i class='fas fa-plus me-1'></i>إضافة نشاط</a></li>
</ul>
</div>
<div class='col-md-6'>
<ul class='list-unstyled'>
<li><a href='index.php' class='btn btn-sm btn-outline-info me-2 mb-2'><i class='fas fa-home me-1'></i>الصفحة الرئيسية</a></li>
<li><a href='login.php' class='btn btn-sm btn-outline-secondary me-2 mb-2'><i class='fas fa-sign-in-alt me-1'></i>تسجيل الدخول</a></li>
</ul>
</div>
</div>
</div>";

echo "</div>
</div>
</div>
</div>
</div>
</body>
</html>";
?>
